import React from 'react';
import {
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useCart, useProducts, useI18n } from '../../hooks';
import { Card, Button, EmptyState } from '../../components';
import { RTLView, RTLText, RTLIcon } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { CartItem } from '../../contexts/DataContext';

interface CartScreenProps {
  navigation: any;
}

export const CartScreen: React.FC<CartScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { cartItems, updateQuantity, removeFromCart, clearCart, cartTotal, cartItemCount } = useCart();
  const { getProductById } = useProducts();
  const { t } = useI18n();

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem(productId);
    } else {
      updateQuantity(productId, newQuantity);
    }
  };

  const handleRemoveItem = (productId: string) => {
    Alert.alert(
      t('cart.removeItem'),
      t('cart.removeItemConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('cart.removeFromCart'), style: 'destructive', onPress: () => removeFromCart(productId) },
      ]
    );
  };

  const handleClearCart = () => {
    Alert.alert(
      t('cart.emptyCart'),
      t('cart.clearCartConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('cart.clearAll'), style: 'destructive', onPress: clearCart },
      ]
    );
  };

  const handleCheckout = () => {
    if (cartItems.length === 0) return;
    
    navigation.navigate('Checkout');
  };

  const renderCartItem = ({ item }: { item: CartItem }) => {
    const product = getProductById(item.productId);
    
    if (!product) {
      return null;
    }

    const itemTotal = product.price * item.quantity;

    return (
      <Card style={styles.cartItem} variant="elevated">
        <RTLView style={styles.itemContent}>
          <RTLView style={styles.productImage}>
            <RTLIcon name="image-outline" size={32} color="#CCCCCC" />
          </RTLView>

          <RTLView style={styles.itemDetails}>
            <RTLText style={styles.productName} numberOfLines={2}>
              {product.name}
            </RTLText>

            <RTLText style={styles.productPrice}>
              {formatCurrency(product.price)} each
            </RTLText>

            {product.originalPrice && product.originalPrice > product.price && (
              <RTLText style={styles.originalPrice}>
                {formatCurrency(product.originalPrice)}
              </RTLText>
            )}

            <RTLView style={styles.quantityContainer}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => handleQuantityChange(item.productId, item.quantity - 1)}
              >
                <RTLIcon name="remove" size={16} color="#666" />
              </TouchableOpacity>

              <RTLText style={styles.quantityText}>{item.quantity}</RTLText>

              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => handleQuantityChange(item.productId, item.quantity + 1)}
              >
                <RTLIcon name="add" size={16} color="#666" />
              </TouchableOpacity>
            </RTLView>
          </RTLView>

          <RTLView style={styles.itemActions}>
            <RTLText style={styles.itemTotal}>
              {formatCurrency(itemTotal)}
            </RTLText>

            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => handleRemoveItem(item.productId)}
            >
              <RTLIcon name="trash-outline" size={20} color="#F44336" />
            </TouchableOpacity>
          </RTLView>
        </RTLView>
      </Card>
    );
  };

  const renderHeader = () => (
    <RTLView style={styles.header}>
      <RTLView style={styles.headerTop}>
        <RTLText style={styles.headerTitle}>{t('cart.shoppingCart')}</RTLText>
        {cartItems.length > 0 && (
          <TouchableOpacity onPress={handleClearCart}>
            <RTLText style={styles.clearAllText}>{t('cart.clearAll')}</RTLText>
          </TouchableOpacity>
        )}
      </RTLView>

      {cartItems.length > 0 && (
        <RTLText style={styles.itemCount}>
          {cartItemCount} {cartItemCount === 1 ? 'item' : t('cart.itemsInCart')}
        </RTLText>
      )}
    </RTLView>
  );

  const renderFooter = () => {
    if (cartItems.length === 0) return null;

    return (
      <RTLView style={styles.footer}>
        <Card style={styles.summaryCard} variant="elevated">
          <RTLView style={styles.summaryContent}>
            <RTLText style={styles.summaryTitle}>{t('cart.orderSummary')}</RTLText>

            <RTLView style={styles.summaryRow}>
              <RTLText style={styles.summaryLabel}>{t('cart.subtotal')}</RTLText>
              <RTLText style={styles.summaryValue}>{formatCurrency(cartTotal)}</RTLText>
            </RTLView>

            <RTLView style={styles.summaryRow}>
              <RTLText style={styles.summaryLabel}>{t('cart.shipping')}</RTLText>
              <RTLText style={styles.summaryValue}>{t('cart.free')}</RTLText>
            </RTLView>

            <RTLView style={styles.summaryRow}>
              <RTLText style={styles.summaryLabel}>{t('cart.tax')}</RTLText>
              <RTLText style={styles.summaryValue}>{t('cart.calculatedAtCheckout')}</RTLText>
            </RTLView>

            <RTLView style={[styles.summaryRow, styles.totalRow]}>
              <RTLText style={styles.totalLabel}>{t('cart.total')}</RTLText>
              <RTLText style={styles.totalValue}>{formatCurrency(cartTotal)}</RTLText>
            </RTLView>
          </RTLView>
        </Card>

        <Button
          title={t('cart.proceedToCheckout')}
          onPress={handleCheckout}
          style={styles.checkoutButton}
          rightIcon={<RTLIcon name="arrow-forward" size={20} color="#FFFFFF" />}
        />

        <Button
          title={t('cart.continueShopping')}
          onPress={() => navigation.navigate('Home')}
          variant="outline"
          style={styles.continueButton}
        />
      </RTLView>
    );
  };

  return (
    <RTLSafeAreaView style={styles.container}>
      <FlatList
        data={cartItems}
        renderItem={renderCartItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={
          <EmptyState
            icon="bag-outline"
            title={t('cart.cartIsEmpty')}
            description={t('cart.addSomeProducts')}
            actionButton={
              <Button
                title={t('cart.startShopping')}
                onPress={() => navigation.navigate('Home')}
                style={styles.emptyActionButton}
              />
            }
          />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: SPACING.lg,
      paddingBottom: SPACING.xl,
    },
    header: {
      marginBottom: SPACING.lg,
    },
    headerTop: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    headerTitle: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    clearAllText: {
      fontSize: FONT_SIZES.sm,
      color: '#F44336',
      fontWeight: FONT_WEIGHTS.medium,
    },
    itemCount: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    cartItem: {
      marginBottom: SPACING.md,
    },
    itemContent: {
      flexDirection: 'row',
      padding: SPACING.md,
    },
    productImage: {
      width: 80,
      height: 80,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BORDER_RADIUS.md,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    itemDetails: {
      flex: 1,
      marginRight: SPACING.md,
    },
    productName: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    productPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    originalPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
      marginBottom: SPACING.sm,
    },
    quantityContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BORDER_RADIUS.sm,
      padding: SPACING.xs,
      alignSelf: 'flex-start',
    },
    quantityButton: {
      width: 32,
      height: 32,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.sm,
    },
    quantityText: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginHorizontal: SPACING.md,
      minWidth: 24,
      textAlign: 'center',
    },
    itemActions: {
      alignItems: 'flex-end',
      justifyContent: 'space-between',
    },
    itemTotal: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    removeButton: {
      padding: SPACING.sm,
    },
    footer: {
      marginTop: SPACING.lg,
    },
    summaryCard: {
      marginBottom: SPACING.lg,
    },
    summaryContent: {
      padding: SPACING.lg,
    },
    summaryTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    summaryLabel: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
    },
    summaryValue: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    totalRow: {
      borderBottomWidth: 0,
      paddingTop: SPACING.md,
      marginTop: SPACING.sm,
      borderTopWidth: 2,
      borderTopColor: colors.border,
    },
    totalLabel: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    totalValue: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.primary,
    },
    checkoutButton: {
      marginBottom: SPACING.md,
    },
    continueButton: {
      marginBottom: SPACING.lg,
    },
    emptyActionButton: {
      marginTop: SPACING.lg,
      alignSelf: 'center',
    },
  });
