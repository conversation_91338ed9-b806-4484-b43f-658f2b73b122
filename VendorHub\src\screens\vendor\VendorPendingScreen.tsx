import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useAuth } from '../../hooks';
import { Card, Button } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
  ICON_SIZES,
} from '../../constants/theme';
import { APP_NAME } from '../../constants';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface VendorPendingScreenProps {
  navigation: any;
}

export const VendorPendingScreen: React.FC<VendorPendingScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  const handleContactSupport = () => {
    // In a real app, this would open email or support chat
    console.log('Contact support');
  };

  return (
    <RTLSafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <LinearGradient colors={GRADIENTS.primary} style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.logoText}>{APP_NAME}</Text>
            <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
              <Ionicons name="log-out-outline" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Main Content */}
        <View style={styles.content}>
          {/* Status Icon */}
          <View style={styles.iconContainer}>
            <View style={styles.iconBackground}>
              <Ionicons name="time-outline" size={ICON_SIZES.xxl} color="#FFFFFF" />
            </View>
          </View>

          {/* Status Message */}
          <Text style={styles.title}>Application Under Review</Text>
          <Text style={styles.subtitle}>
            Thank you for applying to become a vendor on {APP_NAME}!
          </Text>

          {/* Information Card */}
          <Card variant="elevated" style={styles.infoCard}>
            <View style={styles.infoHeader}>
              <Ionicons name="information-circle-outline" size={24} color="#667eea" />
              <Text style={styles.infoTitle}>What happens next?</Text>
            </View>
            
            <View style={styles.infoContent}>
              <View style={styles.infoItem}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>1</Text>
                </View>
                <Text style={styles.infoText}>
                  Our team will review your application and business information
                </Text>
              </View>
              
              <View style={styles.infoItem}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>2</Text>
                </View>
                <Text style={styles.infoText}>
                  We may contact you for additional information if needed
                </Text>
              </View>
              
              <View style={styles.infoItem}>
                <View style={styles.stepNumber}>
                  <Text style={styles.stepNumberText}>3</Text>
                </View>
                <Text style={styles.infoText}>
                  You'll receive an email notification with our decision
                </Text>
              </View>
            </View>
          </Card>

          {/* Application Details */}
          <Card variant="outlined" style={styles.detailsCard}>
            <Text style={styles.detailsTitle}>Application Details</Text>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Business Name:</Text>
              <Text style={styles.detailValue}>{user?.businessName || 'N/A'}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Owner Name:</Text>
              <Text style={styles.detailValue}>{user?.name}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Email:</Text>
              <Text style={styles.detailValue}>{user?.email}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Application Date:</Text>
              <Text style={styles.detailValue}>
                {new Date(user?.createdAt || '').toLocaleDateString()}
              </Text>
            </View>
          </Card>

          {/* Timeline */}
          <Card variant="glass" style={styles.timelineCard}>
            <Text style={styles.timelineTitle}>Typical Review Timeline</Text>
            <Text style={styles.timelineText}>
              Most applications are reviewed within 2-3 business days. 
              Complex applications may take up to 5 business days.
            </Text>
          </Card>

          {/* Actions */}
          <View style={styles.actions}>
            <Button
              title="Contact Support"
              onPress={handleContactSupport}
              variant="outline"
              style={styles.supportButton}
              leftIcon={<Ionicons name="mail-outline" size={20} color="#667eea" />}
            />
            
            <Button
              title="Sign Out"
              onPress={handleLogout}
              variant="ghost"
              style={styles.logoutActionButton}
            />
          </View>
        </View>
      </ScrollView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
    },
    header: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.lg,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    logoText: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    logoutButton: {
      padding: SPACING.sm,
    },
    content: {
      flex: 1,
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.xl,
      alignItems: 'center',
    },
    iconContainer: {
      marginBottom: SPACING.xl,
    },
    iconBackground: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: '#FF9800',
      justifyContent: 'center',
      alignItems: 'center',
    },
    title: {
      fontSize: FONT_SIZES.xxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      textAlign: 'center',
      marginBottom: SPACING.sm,
    },
    subtitle: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: SPACING.xl,
      lineHeight: FONT_SIZES.md * 1.4,
    },
    infoCard: {
      width: '100%',
      marginBottom: SPACING.lg,
    },
    infoHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.md,
    },
    infoTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginLeft: SPACING.sm,
    },
    infoContent: {
      gap: SPACING.md,
    },
    infoItem: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    stepNumber: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
      marginTop: 2,
    },
    stepNumberText: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
    },
    infoText: {
      flex: 1,
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: FONT_SIZES.sm * 1.4,
    },
    detailsCard: {
      width: '100%',
      marginBottom: SPACING.lg,
    },
    detailsTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      borderBottomWidth: 1,
      borderBottomColor: colors.borderLight,
    },
    detailLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    detailValue: {
      fontSize: FONT_SIZES.sm,
      color: colors.textPrimary,
      fontWeight: FONT_WEIGHTS.medium,
      textAlign: 'right',
      flex: 1,
      marginLeft: SPACING.md,
    },
    timelineCard: {
      width: '100%',
      marginBottom: SPACING.xl,
      alignItems: 'center',
    },
    timelineTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    timelineText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textAlign: 'center',
      lineHeight: FONT_SIZES.sm * 1.4,
    },
    actions: {
      width: '100%',
      gap: SPACING.md,
    },
    supportButton: {
      justifyContent: 'center',
    },
    logoutActionButton: {
      backgroundColor: 'transparent',
    },
  });
