import React from 'react';
import { StyleSheet, RefreshControl, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useData, useAuth, useI18n } from '../../hooks';
import { RTLView, RTLText, RTLScrollView, RTLIcon, RTLSafeAreaView } from '../../components/RTL';
import { Card, StatisticsCounter, Button, LoadingSpinner, Chart, StatisticsCard, NotificationCenter, NotificationBadge } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
  ICON_SIZES,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import { notificationService } from '../../services/NotificationService';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface AdminDashboardScreenProps {
  navigation: any;
}

export const AdminDashboardScreen: React.FC<AdminDashboardScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user, logout } = useAuth();
  const { getPlatformStats, isLoading } = useData();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = React.useState(false);
  const [showNotifications, setShowNotifications] = React.useState(false);

  const stats = getPlatformStats();

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleDemoNotifications = () => {
    try {
      if (!notificationService || typeof notificationService.simulateNotifications !== 'function') {
        console.warn('NotificationService not available');
        return;
      }
      notificationService.simulateNotifications();
    } catch (error) {
      console.error('Error simulating notifications:', error);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  const quickActions = [
    {
      title: t('admin.manageVendors'),
      subtitle: `${stats.totalVendors - stats.approvedVendors} ${t('admin.waiting')}`,
      icon: 'storefront-outline',
      color: '#FF9800',
      onPress: () => navigation.navigate('Vendors'),
    },
    {
      title: t('admin.viewOrders'),
      subtitle: `${stats.totalOrders} ${t('admin.totalOrdersText')}`,
      icon: 'receipt-outline',
      color: '#4CAF50',
      onPress: () => navigation.navigate('Orders'),
    },
    {
      title: t('admin.viewProducts'),
      subtitle: `${stats.totalProducts} ${t('admin.productsText')}`,
      icon: 'cube-outline',
      color: '#2196F3',
      onPress: () => navigation.navigate('Products'),
    },
    {
      title: t('admin.viewAnalytics'),
      subtitle: t('admin.detailedReports'),
      icon: 'analytics-outline',
      color: '#9C27B0',
      onPress: () => {},
    },
  ];

  if (isLoading) {
    return (
      <RTLView style={styles.loadingContainer}>
        <LoadingSpinner size="large" variant="gradient" />
      </RTLView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <LinearGradient colors={GRADIENTS.primary} style={styles.header}>
          <RTLView style={styles.headerContent}>
            <RTLView style={styles.headerLeft}>
              <RTLText style={styles.welcomeText}>{t('common.welcomeBack')}</RTLText>
              <RTLText style={styles.nameText}>{user?.name || t('auth.admin')}</RTLText>
            </RTLView>
            <RTLView style={styles.headerRight}>
              <NotificationBadge
                onPress={() => setShowNotifications(true)}
                style={styles.notificationButton}
              />
              <TouchableOpacity onPress={handleLogout} style={styles.logoutButton}>
                <RTLIcon name="log-out-outline" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </RTLView>
          </RTLView>
        </LinearGradient>

        {/* Statistics Cards */}
        <RTLView style={styles.statsContainer}>
          <RTLView style={styles.statsRow}>
            <Card style={styles.statCard} variant="elevated">
              <StatisticsCounter
                value={stats.totalRevenue}
                label={t('admin.totalRevenue')}
                format="currency"
                size="medium"
              />
            </Card>
            <Card style={styles.statCard} variant="elevated">
              <StatisticsCounter
                value={stats.totalVendors}
                label={t('admin.totalVendors')}
                size="medium"
              />
            </Card>
          </RTLView>

          <RTLView style={styles.statsRow}>
            <Card style={styles.statCard} variant="elevated">
              <StatisticsCounter
                value={stats.approvedVendors}
                label={t('admin.approvedVendors')}
                size="medium"
              />
            </Card>
            <Card style={styles.statCard} variant="elevated">
              <StatisticsCounter
                value={stats.pendingVendors}
                label={t('admin.pendingApproval')}
                size="medium"
              />
            </Card>
          </RTLView>
        </RTLView>

        {/* Analytics Charts */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('admin.analyticsOverview')}</RTLText>

          {/* Revenue Chart */}
          <Chart
            type="line"
            title={t('admin.revenueTrend')}
            subtitle={t('admin.lastSevenDays')}
            data={{
              labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
              datasets: [{
                data: [1200, 1800, 1500, 2200, 1900, 2500, 2100],
                color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
                strokeWidth: 3,
              }],
            }}
            height={200}
          />

          {/* Vendor Status Distribution */}
          <Chart
            type="pie"
            title={t('admin.vendorStatusDistribution')}
            subtitle={t('admin.currentVendorBreakdown')}
            data={{
              labels: ['Approved', 'Pending', 'Rejected'],
              data: [stats.approvedVendors, stats.pendingVendors, stats.totalVendors - stats.approvedVendors - stats.pendingVendors],
            }}
            height={200}
          />

          {/* Order Statistics */}
          <Chart
            type="bar"
            title={t('admin.ordersByCategory')}
            subtitle={t('admin.thisMonth')}
            data={{
              labels: ['Electronics', 'Clothing', 'Books', 'Home', 'Sports'],
              datasets: [{
                data: [45, 32, 28, 19, 15],
              }],
            }}
            height={200}
          />

          {/* Performance Metrics */}
          <RTLView style={styles.metricsRow}>
            <StatisticsCard
              title={t('admin.conversionRate')}
              value={12.5}
              format="percentage"
              icon="trending-up-outline"
              trend={{
                value: 2.3,
                isPositive: true,
                period: 'vs last month',
              }}
              style={styles.metricCard}
            />
            <StatisticsCard
              title={t('admin.averageOrderValue')}
              value={stats.totalRevenue / Math.max(stats.totalOrders, 1)}
              format="currency"
              icon="cash-outline"
              trend={{
                value: 5.7,
                isPositive: true,
                period: 'vs last month',
              }}
              style={styles.metricCard}
            />
          </RTLView>

          <RTLView style={styles.metricsRow}>
            <StatisticsCard
              title={t('admin.approvedVendors')}
              value={stats.approvedVendors}
              format="number"
              icon="storefront-outline"
              trend={{
                value: 8.2,
                isPositive: true,
                period: 'vs last month',
              }}
              style={styles.metricCard}
            />
            <StatisticsCard
              title={t('admin.customerSatisfaction')}
              value={4.8}
              subtitle="out of 5.0"
              icon="star-outline"
              trend={{
                value: 0.3,
                isPositive: true,
                period: 'vs last month',
              }}
              style={styles.metricCard}
            />
          </RTLView>
        </RTLView>

        {/* Quick Actions */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('admin.quickActions')}</RTLText>
          <RTLView style={styles.actionsGrid}>
            {quickActions.map((action, index) => (
              <Card
                key={index}
                style={styles.actionCard}
                variant="elevated"
                onPress={action.onPress}
              >
                <RTLView style={styles.actionContent}>
                  <RTLView style={[styles.actionIcon, { backgroundColor: action.color }]}>
                    <RTLIcon name={action.icon as any} size={24} color="#FFFFFF" />
                  </RTLView>
                  <RTLText style={styles.actionTitle}>{action.title}</RTLText>
                  <RTLText style={styles.actionSubtitle}>{action.subtitle}</RTLText>
                </RTLView>
              </Card>
            ))}
          </RTLView>
        </RTLView>

        {/* Platform Overview */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('admin.platformOverview')}</RTLText>
          <Card variant="elevated" style={styles.overviewCard}>
            <RTLView style={styles.overviewRow}>
              <RTLView style={styles.overviewItem}>
                <RTLText style={styles.overviewValue}>{stats.totalProducts}</RTLText>
                <RTLText style={styles.overviewLabel}>{t('nav.products')}</RTLText>
              </RTLView>
              <RTLView style={styles.overviewItem}>
                <RTLText style={styles.overviewValue}>{stats.totalOrders}</RTLText>
                <RTLText style={styles.overviewLabel}>{t('nav.orders')}</RTLText>
              </RTLView>
              <RTLView style={styles.overviewItem}>
                <RTLText style={styles.overviewValue}>
                  {formatCurrency(stats.totalRevenue / stats.totalOrders || 0)}
                </RTLText>
                <RTLText style={styles.overviewLabel}>{t('admin.averageOrderValue')}</RTLText>
              </RTLView>
            </RTLView>
          </Card>
        </RTLView>

        {/* Management Actions */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('admin.management')}</RTLText>
          <RTLView style={styles.managementActions}>
            <Button
              title={t('admin.manageVendors')}
              onPress={() => navigation.navigate('Vendors')}
              variant="outline"
              style={styles.managementButton}
              leftIcon={<RTLIcon name="storefront-outline" size={20} color="#667eea" />}
            />
            <Button
              title={t('admin.viewAllProducts')}
              onPress={() => navigation.navigate('Products')}
              variant="outline"
              style={styles.managementButton}
              leftIcon={<RTLIcon name="cube-outline" size={20} color="#667eea" />}
            />
            <Button
              title={t('admin.orderManagement')}
              onPress={() => navigation.navigate('Orders')}
              variant="outline"
              style={styles.managementButton}
              leftIcon={<RTLIcon name="receipt-outline" size={20} color="#667eea" />}
            />
            <Button
              title={t('admin.demoNotifications')}
              onPress={handleDemoNotifications}
              variant="outline"
              style={styles.managementButton}
              leftIcon={<RTLIcon name="notifications-outline" size={20} color="#667eea" />}
            />
          </RTLView>
        </RTLView>
      </RTLScrollView>

      {/* Notification Center */}
      <NotificationCenter
        visible={showNotifications}
        onClose={() => setShowNotifications(false)}
        onNotificationPress={(notification) => {
          // Handle notification press based on type
          setShowNotifications(false);
          // Add navigation logic here based on notification type
        }}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: SPACING.xl,
    },
    header: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.xl,
      marginBottom: SPACING.lg,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerLeft: {
      flex: 1,
    },
    headerRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    notificationButton: {
      marginRight: SPACING.sm,
    },
    welcomeText: {
      fontSize: FONT_SIZES.md,
      color: '#FFFFFF',
      opacity: 0.8,
    },
    nameText: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
      marginTop: SPACING.xs,
    },
    logoutButton: {
      padding: SPACING.sm,
    },
    statsContainer: {
      paddingHorizontal: SPACING.lg,
      marginBottom: SPACING.lg,
    },
    statsRow: {
      flexDirection: 'row',
      marginBottom: SPACING.md,
    },
    statCard: {
      flex: 1,
      marginHorizontal: SPACING.xs,
    },
    section: {
      paddingHorizontal: SPACING.lg,
      marginBottom: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    actionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginHorizontal: -SPACING.xs,
    },
    actionCard: {
      width: '48%',
      marginHorizontal: '1%',
      marginBottom: SPACING.md,
    },
    actionContent: {
      alignItems: 'center',
      paddingVertical: SPACING.md,
    },
    actionIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    actionTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      textAlign: 'center',
      marginBottom: SPACING.xs,
    },
    actionSubtitle: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    overviewCard: {
      paddingVertical: SPACING.lg,
    },
    overviewRow: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    overviewItem: {
      alignItems: 'center',
    },
    overviewValue: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    overviewLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    managementActions: {
      gap: SPACING.md,
    },
    managementButton: {
      justifyContent: 'flex-start',
      paddingHorizontal: SPACING.lg,
    },
    metricsRow: {
      flexDirection: 'row',
      marginBottom: SPACING.md,
      gap: SPACING.md,
    },
    metricCard: {
      flex: 1,
    },
  });
