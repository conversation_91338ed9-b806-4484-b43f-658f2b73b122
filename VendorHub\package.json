{"name": "<PERSON><PERSON><PERSON>", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "inspect:arabic": "node ./scripts/arabic-translation-inspector.js", "test:inspector": "node ./scripts/test-inspector.js", "validate:translations": "npm run inspect:arabic"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.5.7", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.5.3", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-device": "^7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.7", "expo-media-library": "^17.1.7", "expo-notifications": "^0.31.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.60.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}