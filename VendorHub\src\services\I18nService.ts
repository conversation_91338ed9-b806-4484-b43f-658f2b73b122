import { EventEmitter } from '../utils/EventEmitter';
import { storage } from '../utils/storage';
import { CURRENCY } from '../constants';
import { I18nManager } from 'react-native';
import RNRestart from 'react-native-restart';

export type SupportedLanguage = 'en' | 'ar';

export interface LanguageConfig {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
  currency: string;
  dateFormat: string;
  numberFormat: {
    decimal: string;
    thousands: string;
  };
}

export interface TranslationKeys {
  // Common
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    confirm: string;
    save: string;
    delete: string;
    edit: string;
    add: string;
    search: string;
    filter: string;
    sort: string;
    refresh: string;
    retry: string;
    back: string;
    next: string;
    previous: string;
    done: string;
    close: string;
    yes: string;
    no: string;
    selectLanguage: string;
    welcomeBack: string;
    seeAll: string;
  };

  // Authentication
  auth: {
    login: string;
    logout: string;
    register: string;
    email: string;
    password: string;
    confirmPassword: string;
    forgotPassword: string;
    resetPassword: string;
    welcome: string;
    signIn: string;
    signUp: string;
    createAccount: string;
    alreadyHaveAccount: string;
    dontHaveAccount: string;
    emailRequired: string;
    emailInvalid: string;
    passwordRequired: string;
    passwordTooShort: string;
    emailPlaceholder: string;
    passwordPlaceholder: string;
    namePlaceholder: string;
    confirmPasswordPlaceholder: string;
    passwordMinimum: string;
    name: string;
    phone: string;
    signingIn: string;
    welcomeDescription: string;
    chooseRole: string;
    admin: string;
    adminDescription: string;
    vendor: string;
    vendorDescription: string;
    customer: string;
    customerDescription: string;
    newToVendorHub: string;
    vendorRegistration: string;
    vendorRegistrationDescription: string;
    createAccountDescription: string;
  };

  // Admin
  admin: {
    totalRevenue: string;
    totalVendors: string;
    approvedVendors: string;
    pendingApproval: string;
    analyticsOverview: string;
    revenueTrend: string;
    lastSevenDays: string;
    vendorStatusDistribution: string;
    currentVendorBreakdown: string;
    ordersByCategory: string;
    thisMonth: string;
    conversionRate: string;
    averageOrderValue: string;
    customerSatisfaction: string;
    quickActions: string;
    manageVendors: string;
    viewProducts: string;
    viewOrders: string;
    viewAnalytics: string;
    platformOverview: string;
    management: string;
    viewAllProducts: string;
    orderManagement: string;
    demoNotifications: string;
  };

  // Vendor
  vendor: {
    overview: string;
    totalRevenue: string;
    totalProducts: string;
    totalOrders: string;
    pendingOrders: string;
    dashboard: string;
    myProducts: string;
    myOrders: string;
    addProduct: string;
    editProduct: string;
    shopSettings: string;
    recentOrders: string;
    quickActions: string;
    manageProducts: string;
    viewOrders: string;
    addNewProduct: string;
    updateShopInfo: string;
  };

  // Home
  home: {
    welcomeTo: string;
    appName: string;
    searchPlaceholder: string;
    categories: string;
    featuredProducts: string;
    bestSelling: string;
    onSale: string;
    viewAll: string;
    noProductsFound: string;
    quickActions: string;
    browseAllShops: string;
    viewCart: string;
    recommendedForYou: string;
    // Category names
    electronics: string;
    fashion: string;
    homeGarden: string;
    sports: string;
    books: string;
    health: string;
  };

  // Navigation
  nav: {
    home: string;
    shops: string;
    cart: string;
    profile: string;
    messages: string;
    dashboard: string;
    vendors: string;
    products: string;
    orders: string;
    analytics: string;
  };

  // Profile
  profile: {
    profile: string;
    personalInformation: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    avatar: string;
    recentOrders: string;
    quickActions: string;
    orderHistory: string;
    shoppingCart: string;
    searchProducts: string;
    settings: string;
    logout: string;
    profileUpdated: string;
    profileUpdateFailed: string;
    orders: string;
    cartItems: string;
    viewAll: string;
    noRecentOrders: string;
    startShoppingToSeeOrders: string;
    editProfile: string;
    saveProfile: string;
    cancelEdit: string;
  };

  // Search
  search: {
    search: string;
    searchPlaceholder: string;
    searchProductsVendorsCategories: string;
    noProductsFound: string;
    adjustSearchFilters: string;
    startSearchingToDiscover: string;
    results: string;
    result: string;
    filtersApplied: string;
    filterApplied: string;
    filters: string;
    sortBy: string;
    viewMode: string;
    gridView: string;
    listView: string;
    clearFilters: string;
    priceRange: string;
    category: string;
    vendor: string;
    rating: string;
    availability: string;
    inStock: string;
    onSale: string;
    freeShipping: string;
    relevance: string;
    priceLowToHigh: string;
    priceHighToLow: string;
    newest: string;
    bestRating: string;
    mostPopular: string;
  };

  // Shops
  shops: {
    shops: string;
    searchShops: string;
    noShopsFound: string;
    adjustSearchFilter: string;
    noApprovedShops: string;
    categories: string;
    all: string;
    general: string;
    newShop: string;
    viewShop: string;
    productsCount: string;
    rating: string;
    shopInfo: string;
    contactShop: string;
    shopProducts: string;
    searchProducts: string;
    noProductsInShop: string;
    adjustProductSearch: string;
    noProductsYet: string;
    gridView: string;
    listView: string;
  };

  // Products
  products: {
    product: string;
    products: string;
    price: string;
    originalPrice: string;
    discount: string;
    inStock: string;
    outOfStock: string;
    lowStock: string;
    rating: string;
    reviews: string;
    addToCart: string;
    buyNow: string;
    description: string;
    specifications: string;
    category: string;
    vendor: string;
    featured: string;
    bestSelling: string;
    onSale: string;
    recommended: string;
    addedToCart: string;
    addedToCartMessage: string;
    continueShopping: string;
    viewCart: string;
    failedToAddToCart: string;
    available: string;
    currentlyUnavailable: string;
    readMore: string;
    readLess: string;
    tags: string;
    productsCount: string;
    // Product management
    addProduct: string;
    editProduct: string;
    updateProduct: string;
    productName: string;
    productDescription: string;
    productPrice: string;
    productOriginalPrice: string;
    productCategory: string;
    productInventory: string;
    productTags: string;
    productImages: string;
    selectCategory: string;
    basicInformation: string;
    pricingInventory: string;
    additionalDetails: string;
    productAddedSuccessfully: string;
    productUpdatedSuccessfully: string;
    failedToAddProduct: string;
    failedToUpdateProduct: string;
    productNamePlaceholder: string;
    productDescriptionPlaceholder: string;
    productPricePlaceholder: string;
    productOriginalPricePlaceholder: string;
    productInventoryPlaceholder: string;
    productTagsPlaceholder: string;
    isActive: string;
    productStatus: string;
  };

  // Cart & Checkout
  cart: {
    cart: string;
    addToCart: string;
    removeFromCart: string;
    quantity: string;
    subtotal: string;
    tax: string;
    shipping: string;
    total: string;
    checkout: string;
    proceedToCheckout: string;
    emptyCart: string;
    cartIsEmpty: string;
    continueShopping: string;
    removeItem: string;
    removeItemConfirmation: string;
    updateCart: string;
    shoppingCart: string;
    clearAll: string;
    clearCartConfirmation: string;
    itemsInCart: string;
    orderSummary: string;
    free: string;
    calculatedAtCheckout: string;
    startShopping: string;
    addSomeProducts: string;
  };

  // Checkout
  checkout: {
    checkout: string;
    shippingAddress: string;
    paymentInformation: string;
    review: string;
    streetAddress: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    paymentMethod: string;
    creditCard: string;
    paypal: string;
    applePay: string;
    googlePay: string;
    bankTransfer: string;
    buyNowPayLater: string;
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardholderName: string;
    placeOrder: string;
    orderPlaced: string;
    orderConfirmation: string;
    thankYou: string;
    orderNumber: string;
    estimatedDelivery: string;
    trackOrder: string;
    continueShoppingAfterOrder: string;
    emptyCartTitle: string;
    emptyCartDescription: string;
    goToShop: string;
    loginRequired: string;
    cartIssues: string;
    cartValidationError: string;
    completeRequiredFields: string;
    processingPayment: string;
    paymentFailed: string;
    paymentSuccess: string;
    edit: string;
  };

  // Orders
  orders: {
    order: string;
    orders: string;
    orderHistory: string;
    orderDetails: string;
    orderNumber: string;
    orderDate: string;
    orderStatus: string;
    pending: string;
    confirmed: string;
    shipped: string;
    delivered: string;
    cancelled: string;
    refunded: string;
    trackOrder: string;
    reorder: string;
    // Additional order details
    orderPlaced: string;
    orderItems: string;
    shippingInformation: string;
    deliveryAddress: string;
    reorderItems: string;
    addAllItemsToCart: string;
    itemsAddedToCart: string;
    orderNotFound: string;
    orderNotFoundDescription: string;
    leaveReview: string;
    featureComingSoon: string;
    reviewFunctionalityComingSoon: string;
    noOrdersFound: string;
  };

  // Error messages
  errors: {
    productNotFound: string;
    productNotFoundDescription: string;
    goBack: string;
    networkError: string;
    tryAgain: string;
    somethingWentWrong: string;
  };

  // Demo and placeholders
  demo: {
    demoCredentials: string;
    fillDemoCredentials: string;
    adminCredentials: string;
    userCredentials: string;
  };

  // Chat
  chat: {
    conversations: string;
    searchConversations: string;
    noConversationsYet: string;
    startConversationDescription: string;
    browseVendors: string;
    loadingConversations: string;
    loadingChat: string;
    conversationOptions: string;
    whatWouldYouLikeToDo: string;
    mute: string;
    unmute: string;
    archive: string;
    delete: string;
    you: string;
    isTyping: string;
    areTyping: string;
    edited: string;
    typeMessage: string;
    send: string;
    failedToLoadChat: string;
    failedToSendMessage: string;
    messageDeleted: string;
    messageEdited: string;
  };

  // Success messages
  success: {
    loginSuccess: string;
    registrationSuccess: string;
    vendorApproved: string;
    vendorRejected: string;
    productCreated: string;
    productUpdated: string;
    orderPlaced: string;
    orderUpdated: string;
  };
}

const LANGUAGES: Record<SupportedLanguage, LanguageConfig> = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
    currency: CURRENCY.CODE, // Always use BHD
    dateFormat: 'MM/DD/YYYY',
    numberFormat: { decimal: '.', thousands: ',' },
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: 'ع',
    rtl: true,
    currency: CURRENCY.CODE, // Always use BHD
    dateFormat: 'DD/MM/YYYY',
    numberFormat: { decimal: '.', thousands: ',' },
  },
};

class I18nService extends EventEmitter {
  private static instance: I18nService;
  private currentLanguage: SupportedLanguage = 'en';
  private translations: Record<SupportedLanguage, Partial<TranslationKeys>> = {} as any;
  private fallbackLanguage: SupportedLanguage = 'en';

  private constructor() {
    super();
    this.loadStoredLanguage();
    this.loadTranslations();
    this.initializeRTL();
  }

  public static getInstance(): I18nService {
    if (!I18nService.instance) {
      I18nService.instance = new I18nService();
    }
    return I18nService.instance;
  }

  // Language Management
  public async setLanguage(language: SupportedLanguage): Promise<void> {
    if (this.currentLanguage === language) return;

    const currentIsRTL = this.isRTL();
    this.currentLanguage = language;
    const newIsRTL = this.isRTL();

    // If RTL status changed, update I18nManager and restart app
    if (currentIsRTL !== newIsRTL) {
      I18nManager.forceRTL(newIsRTL);
      await storage.setItem('selectedLanguage', language);
      // Trigger app restart to apply RTL changes
      RNRestart.Restart();
    } else {
      await storage.setItem('selectedLanguage', language);
      this.emit('languageChanged', language);
    }
  }

  public getCurrentLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  public getCurrentLanguageConfig(): LanguageConfig {
    return LANGUAGES[this.currentLanguage];
  }

  public getSupportedLanguages(): LanguageConfig[] {
    return Object.values(LANGUAGES);
  }

  public isRTL(): boolean {
    return LANGUAGES[this.currentLanguage].rtl;
  }

  // RTL Management
  private initializeRTL(): void {
    const isRTL = this.isRTL();
    I18nManager.forceRTL(isRTL);
  }

  public getTextAlign(): 'left' | 'right' | 'center' {
    return this.isRTL() ? 'right' : 'left';
  }

  public getFlexDirection(): 'row' | 'row-reverse' {
    return this.isRTL() ? 'row-reverse' : 'row';
  }

  public getWritingDirection(): 'ltr' | 'rtl' {
    return this.isRTL() ? 'rtl' : 'ltr';
  }

  // Translation Methods
  public t(key: string, params?: Record<string, string | number>): string {
    const translation = this.getTranslation(key);

    if (!params) return translation;

    // Replace parameters in translation
    return Object.entries(params).reduce((text, [param, value]) => {
      return text.replace(new RegExp(`{{${param}}}`, 'g'), String(value));
    }, translation);
  }

  private getTranslation(key: string): string {
    const keys = key.split('.');
    let translation: any = this.translations[this.currentLanguage];

    // Navigate through nested keys
    for (const k of keys) {
      translation = translation?.[k];
    }

    // If translation not found, try fallback language
    if (!translation && this.currentLanguage !== this.fallbackLanguage) {
      let fallbackTranslation: any = this.translations[this.fallbackLanguage];
      for (const k of keys) {
        fallbackTranslation = fallbackTranslation?.[k];
      }
      translation = fallbackTranslation;
    }

    // Return key if no translation found
    return translation || key;
  }

  // Formatting Methods
  public formatCurrency(amount: number): string {
    // Always use BHD currency - no currency parameter allowed
    const { decimal, thousands } = this.getCurrentLanguageConfig().numberFormat;

    try {
      return new Intl.NumberFormat(this.currentLanguage, {
        style: 'currency',
        currency: CURRENCY.CODE,
        minimumFractionDigits: CURRENCY.DECIMAL_PLACES,
        maximumFractionDigits: CURRENCY.DECIMAL_PLACES,
      }).format(amount);
    } catch {
      // Fallback formatting with BHD-specific formatting
      const formatted = amount.toFixed(CURRENCY.DECIMAL_PLACES)
        .replace('.', decimal)
        .replace(/\B(?=(\d{3})+(?!\d))/g, thousands);
      return `${CURRENCY.SYMBOL} ${formatted}`;
    }
  }

  public formatNumber(number: number): string {
    const { decimal, thousands } = this.getCurrentLanguageConfig().numberFormat;

    try {
      return new Intl.NumberFormat(this.currentLanguage).format(number);
    } catch {
      return number.toString()
        .replace('.', decimal)
        .replace(/\B(?=(\d{3})+(?!\d))/g, thousands);
    }
  }

  public formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    try {
      return new Intl.DateTimeFormat(this.currentLanguage).format(dateObj);
    } catch {
      return dateObj.toLocaleDateString();
    }
  }

  public formatRelativeTime(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    if (diffInSeconds < 60) return this.t('common.justNow');
    if (diffInSeconds < 3600) return this.t('common.minutesAgo', { minutes: Math.floor(diffInSeconds / 60) });
    if (diffInSeconds < 86400) return this.t('common.hoursAgo', { hours: Math.floor(diffInSeconds / 3600) });
    if (diffInSeconds < 604800) return this.t('common.daysAgo', { days: Math.floor(diffInSeconds / 86400) });

    return this.formatDate(dateObj);
  }

  // Private Methods
  private async loadStoredLanguage(): Promise<void> {
    try {
      const stored = await storage.getItem('selectedLanguage');
      if (stored && Object.keys(LANGUAGES).includes(stored)) {
        this.currentLanguage = stored as SupportedLanguage;
      }
    } catch (error) {
      console.error('Error loading stored language:', error);
    }
  }

  private loadTranslations(): void {
    // Load English translations (base)
    this.translations.en = {
      common: {
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        cancel: 'Cancel',
        confirm: 'Confirm',
        save: 'Save',
        delete: 'Delete',
        edit: 'Edit',
        add: 'Add',
        search: 'Search',
        filter: 'Filter',
        sort: 'Sort',
        refresh: 'Refresh',
        retry: 'Retry',
        back: 'Back',
        next: 'Next',
        previous: 'Previous',
        done: 'Done',
        close: 'Close',
        yes: 'Yes',
        no: 'No',
        selectLanguage: 'Select Language',
        welcomeBack: 'Welcome back,',
        seeAll: 'See All',
      },
      auth: {
        login: 'Login',
        logout: 'Logout',
        register: 'Register',
        email: 'Email',
        password: 'Password',
        confirmPassword: 'Confirm Password',
        forgotPassword: 'Forgot Password?',
        resetPassword: 'Reset Password',
        welcome: 'Multi-Vendor Mobile Marketplace',
        signIn: 'Sign In',
        signUp: 'Sign Up',
        createAccount: 'Create Account',
        alreadyHaveAccount: 'Already have an account?',
        dontHaveAccount: "Don't have an account?",
        emailRequired: 'Email is required',
        emailInvalid: 'Please enter a valid email',
        passwordRequired: 'Password is required',
        passwordTooShort: 'Password must be at least 6 characters',
        emailPlaceholder: 'Enter your email',
        passwordPlaceholder: 'Enter your password',
        namePlaceholder: 'Enter your full name',
        confirmPasswordPlaceholder: 'Confirm your password',
        passwordMinimum: 'Minimum 6 characters',
        name: 'Full Name',
        phone: 'Phone Number',
        signingIn: 'Signing In...',
        welcomeDescription: 'Connect vendors and customers in one seamless platform',
        chooseRole: 'Choose Your Role',
        admin: 'Admin',
        adminDescription: 'Manage vendors, oversee platform operations, and monitor analytics',
        vendor: 'Vendor',
        vendorDescription: 'Sell your products, manage inventory, and grow your business',
        customer: 'Customer',
        customerDescription: 'Discover products, shop from multiple vendors, and enjoy great deals',
        newToVendorHub: 'New to VendorHub? Register',
        vendorRegistration: 'Vendor Registration',
        vendorRegistrationDescription: 'Join our marketplace and start selling your products',
        createAccountDescription: 'Join thousands of happy customers',
      },
      admin: {
        totalRevenue: 'Total Revenue',
        totalVendors: 'Total Vendors',
        approvedVendors: 'Approved Vendors',
        pendingApproval: 'Pending Approval',
        analyticsOverview: 'Analytics Overview',
        revenueTrend: 'Revenue Trend',
        lastSevenDays: 'Last 7 days',
        vendorStatusDistribution: 'Vendor Status Distribution',
        currentVendorBreakdown: 'Current vendor breakdown',
        ordersByCategory: 'Orders by Category',
        thisMonth: 'This month',
        conversionRate: 'Conversion Rate',
        averageOrderValue: 'Average Order Value',
        customerSatisfaction: 'Customer Satisfaction',
        quickActions: 'Quick Actions',
        manageVendors: 'Manage Vendors',
        viewProducts: 'View Products',
        viewOrders: 'View Orders',
        viewAnalytics: 'View Analytics',
        platformOverview: 'Platform Overview',
        management: 'Management',
        viewAllProducts: 'View All Products',
        orderManagement: 'Order Management',
        demoNotifications: 'Demo Notifications',
      },
      vendor: {
        overview: 'Overview',
        totalRevenue: 'Total Revenue',
        totalProducts: 'Total Products',
        totalOrders: 'Total Orders',
        pendingOrders: 'Pending Orders',
        dashboard: 'Dashboard',
        myProducts: 'My Products',
        myOrders: 'My Orders',
        addProduct: 'Add Product',
        editProduct: 'Edit Product',
        shopSettings: 'Shop Settings',
        recentOrders: 'Recent Orders',
        quickActions: 'Quick Actions',
        manageProducts: 'Manage Products',
        viewOrders: 'View Orders',
        addNewProduct: 'Add New Product',
        updateShopInfo: 'Update Shop Info',
      },
      home: {
        welcomeTo: 'Welcome to',
        appName: 'VendorHub',
        searchPlaceholder: 'Search products...',
        categories: 'Categories',
        featuredProducts: 'Featured Products',
        bestSelling: 'Best Selling',
        onSale: 'On Sale',
        viewAll: 'View All',
        noProductsFound: 'No products found',
        quickActions: 'Quick Actions',
        browseAllShops: 'Browse All Shops',
        viewCart: 'View Cart',
        recommendedForYou: 'Recommended for You',
        // Category names
        electronics: 'Electronics',
        fashion: 'Fashion',
        homeGarden: 'Home & Garden',
        sports: 'Sports',
        books: 'Books',
        health: 'Health',
      },
      nav: {
        home: 'Home',
        shops: 'Shops',
        cart: 'Cart',
        profile: 'Profile',
        messages: 'Messages',
        dashboard: 'Dashboard',
        vendors: 'Vendors',
        products: 'Products',
        orders: 'Orders',
        analytics: 'Analytics',
      },
      chat: {
        conversations: 'Conversations',
        searchConversations: 'Search conversations...',
        noConversationsYet: 'No conversations yet',
        startConversationDescription: 'Start a conversation with a vendor or customer to see it here',
        browseVendors: 'Browse Vendors',
        loadingConversations: 'Loading conversations...',
        loadingChat: 'Loading chat...',
        conversationOptions: 'Conversation Options',
        whatWouldYouLikeToDo: 'What would you like to do?',
        mute: 'Mute',
        unmute: 'Unmute',
        archive: 'Archive',
        delete: 'Delete',
        you: 'You: ',
        isTyping: 'is typing...',
        areTyping: 'are typing...',
        edited: 'edited',
        typeMessage: 'Type a message...',
        send: 'Send',
        failedToLoadChat: 'Failed to load chat',
        failedToSendMessage: 'Failed to send message',
        messageDeleted: 'This message was deleted',
        messageEdited: 'This message was edited',
      },
      profile: {
        profile: 'Profile',
        personalInformation: 'Personal Information',
        name: 'Name',
        email: 'Email',
        phone: 'Phone',
        address: 'Address',
        avatar: 'Avatar',
        recentOrders: 'Recent Orders',
        quickActions: 'Quick Actions',
        orderHistory: 'Order History',
        shoppingCart: 'Shopping Cart',
        searchProducts: 'Search Products',
        settings: 'Settings',
        logout: 'Logout',
        profileUpdated: 'Profile updated successfully!',
        profileUpdateFailed: 'Failed to update profile. Please try again.',
        orders: 'Orders',
        cartItems: 'Cart Items',
        viewAll: 'View All',
        noRecentOrders: 'No recent orders',
        startShoppingToSeeOrders: 'Start shopping to see your orders here',
        editProfile: 'Edit Profile',
        saveProfile: 'Save Profile',
        cancelEdit: 'Cancel',
      },
      search: {
        search: 'Search',
        searchPlaceholder: 'Search products...',
        searchProductsVendorsCategories: 'Search products, vendors, categories...',
        noProductsFound: 'No products found',
        adjustSearchFilters: 'Try adjusting your search or filters',
        startSearchingToDiscover: 'Start searching to discover products',
        results: 'results',
        result: 'result',
        filtersApplied: 'filters applied',
        filterApplied: 'filter applied',
        filters: 'Filters',
        sortBy: 'Sort by',
        viewMode: 'View mode',
        gridView: 'Grid View',
        listView: 'List View',
        clearFilters: 'Clear Filters',
        priceRange: 'Price Range',
        category: 'Category',
        vendor: 'Vendor',
        rating: 'Rating',
        availability: 'Availability',
        inStock: 'In Stock',
        onSale: 'On Sale',
        freeShipping: 'Free Shipping',
        relevance: 'Relevance',
        priceLowToHigh: 'Price: Low to High',
        priceHighToLow: 'Price: High to Low',
        newest: 'Newest',
        bestRating: 'Best Rating',
        mostPopular: 'Most Popular',
      },
      shops: {
        shops: 'Shops',
        searchShops: 'Search shops...',
        noShopsFound: 'No shops found',
        adjustSearchFilter: 'Try adjusting your search or category filter',
        noApprovedShops: 'No approved shops are available yet',
        categories: 'Categories',
        all: 'All',
        general: 'General',
        newShop: 'New',
        viewShop: 'View Shop',
        productsCount: 'products',
        rating: 'Rating',
        shopInfo: 'Shop Info',
        contactShop: 'Contact Shop',
        shopProducts: 'Shop Products',
        searchProducts: 'Search products...',
        noProductsInShop: 'No products found',
        adjustProductSearch: 'Try adjusting your search or category filter',
        noProductsYet: "This shop doesn't have any products yet",
        gridView: 'Grid View',
        listView: 'List View',
      },
      products: {
        product: 'Product',
        products: 'Products',
        price: 'Price',
        originalPrice: 'Original Price',
        discount: 'Discount',
        inStock: 'In Stock',
        outOfStock: 'Out of Stock',
        lowStock: 'Low Stock',
        rating: 'Rating',
        reviews: 'Reviews',
        addToCart: 'Add to Cart',
        buyNow: 'Buy Now',
        description: 'Description',
        specifications: 'Specifications',
        category: 'Category',
        vendor: 'Vendor',
        featured: 'Featured',
        bestSelling: 'Best Selling',
        onSale: 'On Sale',
        recommended: 'Recommended',
        addedToCart: 'Added to Cart',
        addedToCartMessage: 'has been added to your cart.',
        continueShopping: 'Continue Shopping',
        viewCart: 'View Cart',
        failedToAddToCart: 'Failed to add product to cart. Please try again.',
        available: 'available',
        currentlyUnavailable: 'Currently unavailable',
        readMore: 'Read More',
        readLess: 'Read Less',
        tags: 'Tags',
        productsCount: 'products',
        // Product management
        addProduct: 'Add Product',
        editProduct: 'Edit Product',
        updateProduct: 'Update Product',
        productName: 'Product Name',
        productDescription: 'Product Description',
        productPrice: 'Product Price',
        productOriginalPrice: 'Original Price (Optional)',
        productCategory: 'Product Category',
        productInventory: 'Inventory',
        productTags: 'Tags (Optional)',
        productImages: 'Product Images',
        selectCategory: 'Select Category',
        basicInformation: 'Basic Information',
        pricingInventory: 'Pricing & Inventory',
        additionalDetails: 'Additional Details',
        productAddedSuccessfully: 'Product added successfully!',
        productUpdatedSuccessfully: 'Product updated successfully!',
        failedToAddProduct: 'Failed to add product. Please try again.',
        failedToUpdateProduct: 'Failed to update product. Please try again.',
        productNamePlaceholder: 'Enter product name',
        productDescriptionPlaceholder: 'Describe your product...',
        productPricePlaceholder: 'Enter price in BHD',
        productOriginalPricePlaceholder: 'Enter original price (if on sale)',
        productInventoryPlaceholder: 'Enter quantity available',
        productTagsPlaceholder: 'e.g., wireless, bluetooth, premium',
        isActive: 'Active',
        productStatus: 'Product Status',
      },
      cart: {
        cart: 'Cart',
        addToCart: 'Add to Cart',
        removeFromCart: 'Remove from Cart',
        quantity: 'Quantity',
        subtotal: 'Subtotal',
        tax: 'Tax',
        shipping: 'Shipping',
        total: 'Total',
        checkout: 'Checkout',
        proceedToCheckout: 'Proceed to Checkout',
        emptyCart: 'Empty Cart',
        cartIsEmpty: 'Your cart is empty',
        continueShopping: 'Continue Shopping',
        removeItem: 'Remove Item',
        removeItemConfirmation: 'Are you sure you want to remove this item from your cart?',
        updateCart: 'Update Cart',
        shoppingCart: 'Shopping Cart',
        clearAll: 'Clear All',
        clearCartConfirmation: 'Are you sure you want to remove all items from your cart?',
        itemsInCart: 'items in your cart',
        orderSummary: 'Order Summary',
        free: 'Free',
        calculatedAtCheckout: 'Calculated at checkout',
        startShopping: 'Start Shopping',
        addSomeProducts: 'Add some products to get started',
      },
      checkout: {
        checkout: 'Checkout',
        shippingAddress: 'Shipping Address',
        paymentInformation: 'Payment Information',
        review: 'Review',
        streetAddress: 'Street Address',
        city: 'City',
        state: 'State',
        zipCode: 'ZIP Code',
        country: 'Country',
        paymentMethod: 'Payment Method',
        creditCard: 'Credit Card',
        paypal: 'PayPal',
        applePay: 'Apple Pay',
        googlePay: 'Google Pay',
        bankTransfer: 'Bank Transfer',
        buyNowPayLater: 'Buy Now, Pay Later',
        cardNumber: 'Card Number',
        expiryDate: 'Expiry Date',
        cvv: 'CVV',
        cardholderName: 'Cardholder Name',
        placeOrder: 'Place Order',
        orderPlaced: 'Order Placed',
        orderConfirmation: 'Order Confirmation',
        thankYou: 'Thank you for your order!',
        orderNumber: 'Order Number',
        estimatedDelivery: 'Estimated Delivery',
        trackOrder: 'Track Order',
        continueShoppingAfterOrder: 'Continue Shopping',
        emptyCartTitle: 'Your cart is empty',
        emptyCartDescription: 'Add some items to your cart before checking out',
        goToShop: 'Go to Shop',
        loginRequired: 'Please log in to place an order',
        cartIssues: 'Cart Issues',
        cartValidationError: 'Some items in your cart are no longer available. Please review your cart.',
        completeRequiredFields: 'Please complete all required fields',
        processingPayment: 'Processing payment...',
        paymentFailed: 'Payment failed. Please try again.',
        paymentSuccess: 'Payment successful!',
        edit: 'Edit',
      },
      orders: {
        order: 'Order',
        orders: 'Orders',
        orderHistory: 'Order History',
        orderDetails: 'Order Details',
        orderNumber: 'Order Number',
        orderDate: 'Order Date',
        orderStatus: 'Order Status',
        pending: 'Pending',
        confirmed: 'Confirmed',
        shipped: 'Shipped',
        delivered: 'Delivered',
        cancelled: 'Cancelled',
        refunded: 'Refunded',
        trackOrder: 'Track Order',
        reorder: 'Reorder',
        // Additional order details
        orderPlaced: 'Order Placed',
        orderItems: 'Order Items',
        shippingInformation: 'Shipping Information',
        deliveryAddress: 'Delivery Address',
        reorderItems: 'Reorder Items',
        addAllItemsToCart: 'Add all items from this order to your cart?',
        itemsAddedToCart: 'Items added to cart!',
        orderNotFound: 'Order Not Found',
        orderNotFoundDescription: "The order you're looking for doesn't exist or has been removed.",
        leaveReview: 'Leave Review',
        featureComingSoon: 'Feature Coming Soon',
        reviewFunctionalityComingSoon: 'Review functionality will be available soon!',
        noOrdersFound: 'No orders found',
      },
      errors: {
        productNotFound: 'Product Not Found',
        productNotFoundDescription: "The product you're looking for doesn't exist or has been removed.",
        goBack: 'Go Back',
        networkError: 'Network Error',
        tryAgain: 'Try Again',
        somethingWentWrong: 'Something went wrong',
      },
      demo: {
        demoCredentials: 'Demo Credentials',
        fillDemoCredentials: 'Fill Demo Credentials',
        adminCredentials: 'Admin Credentials',
        userCredentials: 'User Credentials',
      },
      success: {
        loginSuccess: 'Login successful!',
        registrationSuccess: 'Registration successful!',
        vendorApproved: 'Vendor approved successfully!',
        vendorRejected: 'Vendor rejected successfully!',
        productCreated: 'Product created successfully!',
        productUpdated: 'Product updated successfully!',
        orderPlaced: 'Order placed successfully!',
        orderUpdated: 'Order status updated successfully!',
      },
    };

    // Load Arabic translations
    this.translations.ar = {
      common: {
        loading: 'جاري التحميل...',
        error: 'خطأ',
        success: 'نجح',
        cancel: 'إلغاء',
        confirm: 'تأكيد',
        save: 'حفظ',
        delete: 'حذف',
        edit: 'تعديل',
        add: 'إضافة',
        search: 'بحث',
        filter: 'تصفية',
        sort: 'ترتيب',
        refresh: 'تحديث',
        retry: 'إعادة المحاولة',
        back: 'رجوع',
        next: 'التالي',
        previous: 'السابق',
        done: 'تم',
        close: 'إغلاق',
        yes: 'نعم',
        no: 'لا',
        selectLanguage: 'اختر اللغة',
        welcomeBack: 'أهلاً بعودتك،',
        seeAll: 'عرض الكل',
      },
      auth: {
        login: 'تسجيل الدخول',
        logout: 'تسجيل الخروج',
        register: 'التسجيل',
        email: 'البريد الإلكتروني',
        password: 'كلمة المرور',
        confirmPassword: 'تأكيد كلمة المرور',
        forgotPassword: 'نسيت كلمة المرور؟',
        resetPassword: 'إعادة تعيين كلمة المرور',
        welcome: 'مرحباً',
        signIn: 'تسجيل الدخول',
        signUp: 'إنشاء حساب',
        createAccount: 'إنشاء حساب',
        alreadyHaveAccount: 'لديك حساب بالفعل؟',
        dontHaveAccount: 'ليس لديك حساب؟',
        emailRequired: 'البريد الإلكتروني مطلوب',
        emailInvalid: 'يرجى إدخال بريد إلكتروني صحيح',
        passwordRequired: 'كلمة المرور مطلوبة',
        passwordTooShort: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
        emailPlaceholder: 'أدخل بريدك الإلكتروني',
        passwordPlaceholder: 'أدخل كلمة المرور',
        namePlaceholder: 'أدخل اسمك الكامل',
        confirmPasswordPlaceholder: 'أكد كلمة المرور',
        passwordMinimum: 'الحد الأدنى 6 أحرف',
        name: 'الاسم الكامل',
        phone: 'رقم الهاتف',
        signingIn: 'جاري تسجيل الدخول...',
        welcomeDescription: 'ربط التجار والعملاء في منصة واحدة متكاملة',
        chooseRole: 'اختر دورك',
        admin: 'مدير',
        adminDescription: 'إدارة التجار والإشراف على عمليات المنصة ومراقبة التحليلات',
        vendor: 'تاجر',
        vendorDescription: 'بيع منتجاتك وإدارة المخزون وتنمية أعمالك',
        customer: 'عميل',
        customerDescription: 'اكتشف المنتجات وتسوق من تجار متعددين واستمتع بعروض رائعة',
        newToVendorHub: 'جديد على VendorHub؟ سجل الآن',
        vendorRegistration: 'تسجيل التاجر',
        vendorRegistrationDescription: 'انضم إلى سوقنا وابدأ ببيع منتجاتك',
        createAccountDescription: 'انضم إلى آلاف العملاء السعداء',
      },
      admin: {
        totalRevenue: 'إجمالي الإيرادات',
        totalVendors: 'إجمالي التجار',
        approvedVendors: 'التجار المعتمدون',
        pendingApproval: 'في انتظار الموافقة',
        analyticsOverview: 'نظرة عامة على التحليلات',
        revenueTrend: 'اتجاه الإيرادات',
        lastSevenDays: 'آخر 7 أيام',
        vendorStatusDistribution: 'توزيع حالة التجار',
        currentVendorBreakdown: 'تفصيل التجار الحالي',
        ordersByCategory: 'الطلبات حسب الفئة',
        thisMonth: 'هذا الشهر',
        conversionRate: 'معدل التحويل',
        averageOrderValue: 'متوسط قيمة الطلب',
        customerSatisfaction: 'رضا العملاء',
        quickActions: 'إجراءات سريعة',
        manageVendors: 'إدارة التجار',
        viewProducts: 'عرض المنتجات',
        viewOrders: 'عرض الطلبات',
        viewAnalytics: 'عرض التحليلات',
        platformOverview: 'نظرة عامة على المنصة',
        management: 'الإدارة',
        viewAllProducts: 'عرض جميع المنتجات',
        orderManagement: 'إدارة الطلبات',
        demoNotifications: 'إشعارات تجريبية',
      },
      vendor: {
        overview: 'نظرة عامة',
        totalRevenue: 'إجمالي الإيرادات',
        totalProducts: 'إجمالي المنتجات',
        totalOrders: 'إجمالي الطلبات',
        pendingOrders: 'الطلبات المعلقة',
        dashboard: 'لوحة التحكم',
        myProducts: 'منتجاتي',
        myOrders: 'طلباتي',
        addProduct: 'إضافة منتج',
        editProduct: 'تعديل المنتج',
        shopSettings: 'إعدادات المتجر',
        recentOrders: 'الطلبات الأخيرة',
        quickActions: 'إجراءات سريعة',
        manageProducts: 'إدارة المنتجات',
        viewOrders: 'عرض الطلبات',
        addNewProduct: 'إضافة منتج جديد',
        updateShopInfo: 'تحديث معلومات المتجر',
      },
      home: {
        welcomeTo: 'أهلاً بك في',
        appName: 'VendorHub',
        searchPlaceholder: 'البحث عن المنتجات...',
        categories: 'الفئات',
        featuredProducts: 'المنتجات المميزة',
        bestSelling: 'الأكثر مبيعاً',
        onSale: 'في التخفيضات',
        viewAll: 'عرض الكل',
        noProductsFound: 'لم يتم العثور على منتجات',
        quickActions: 'إجراءات سريعة',
        browseAllShops: 'تصفح جميع المتاجر',
        viewCart: 'عرض السلة',
        recommendedForYou: 'مُوصى لك',
        // Category names
        electronics: 'الإلكترونيات',
        fashion: 'الأزياء',
        homeGarden: 'المنزل والحديقة',
        sports: 'الرياضة',
        books: 'الكتب',
        health: 'الصحة',
      },
      nav: {
        home: 'الرئيسية',
        shops: 'المتاجر',
        cart: 'السلة',
        profile: 'الملف الشخصي',
        messages: 'الرسائل',
        dashboard: 'لوحة التحكم',
        vendors: 'التجار',
        products: 'المنتجات',
        orders: 'الطلبات',
        analytics: 'التحليلات',
      },
      chat: {
        conversations: 'المحادثات',
        searchConversations: 'البحث في المحادثات...',
        noConversationsYet: 'لا توجد محادثات بعد',
        startConversationDescription: 'ابدأ محادثة مع تاجر أو عميل لرؤيتها هنا',
        browseVendors: 'تصفح التجار',
        loadingConversations: 'جاري تحميل المحادثات...',
        loadingChat: 'جاري تحميل المحادثة...',
        conversationOptions: 'خيارات المحادثة',
        whatWouldYouLikeToDo: 'ماذا تريد أن تفعل؟',
        mute: 'كتم الصوت',
        unmute: 'إلغاء كتم الصوت',
        archive: 'أرشفة',
        delete: 'حذف',
        you: 'أنت: ',
        isTyping: 'يكتب...',
        areTyping: 'يكتبون...',
        edited: 'معدّل',
        typeMessage: 'اكتب رسالة...',
        send: 'إرسال',
        failedToLoadChat: 'فشل في تحميل المحادثة',
        failedToSendMessage: 'فشل في إرسال الرسالة',
        messageDeleted: 'تم حذف هذه الرسالة',
        messageEdited: 'تم تعديل هذه الرسالة',
      },
      profile: {
        profile: 'الملف الشخصي',
        personalInformation: 'المعلومات الشخصية',
        name: 'الاسم',
        email: 'البريد الإلكتروني',
        phone: 'الهاتف',
        address: 'العنوان',
        avatar: 'الصورة الشخصية',
        recentOrders: 'الطلبات الأخيرة',
        quickActions: 'إجراءات سريعة',
        orderHistory: 'تاريخ الطلبات',
        shoppingCart: 'سلة التسوق',
        searchProducts: 'البحث في المنتجات',
        settings: 'الإعدادات',
        logout: 'تسجيل الخروج',
        profileUpdated: 'تم تحديث الملف الشخصي بنجاح!',
        profileUpdateFailed: 'فشل في تحديث الملف الشخصي. يرجى المحاولة مرة أخرى.',
        orders: 'الطلبات',
        cartItems: 'عناصر السلة',
        viewAll: 'عرض الكل',
        noRecentOrders: 'لا توجد طلبات حديثة',
        startShoppingToSeeOrders: 'ابدأ التسوق لرؤية طلباتك هنا',
        editProfile: 'تعديل الملف الشخصي',
        saveProfile: 'حفظ الملف الشخصي',
        cancelEdit: 'إلغاء',
      },
      search: {
        search: 'البحث',
        searchPlaceholder: 'البحث في المنتجات...',
        searchProductsVendorsCategories: 'البحث في المنتجات والمتاجر والفئات...',
        noProductsFound: 'لم يتم العثور على منتجات',
        adjustSearchFilters: 'جرب تعديل البحث أو الفلاتر',
        startSearchingToDiscover: 'ابدأ البحث لاكتشاف المنتجات',
        results: 'نتائج',
        result: 'نتيجة',
        filtersApplied: 'فلاتر مطبقة',
        filterApplied: 'فلتر مطبق',
        filters: 'الفلاتر',
        sortBy: 'ترتيب حسب',
        viewMode: 'نمط العرض',
        gridView: 'عرض الشبكة',
        listView: 'عرض القائمة',
        clearFilters: 'مسح الفلاتر',
        priceRange: 'نطاق السعر',
        category: 'الفئة',
        vendor: 'المتجر',
        rating: 'التقييم',
        availability: 'التوفر',
        inStock: 'متوفر',
        onSale: 'في التخفيضات',
        freeShipping: 'شحن مجاني',
        relevance: 'الصلة',
        priceLowToHigh: 'السعر: من الأقل إلى الأعلى',
        priceHighToLow: 'السعر: من الأعلى إلى الأقل',
        newest: 'الأحدث',
        bestRating: 'أفضل تقييم',
        mostPopular: 'الأكثر شعبية',
      },
      shops: {
        shops: 'المتاجر',
        searchShops: 'البحث في المتاجر...',
        noShopsFound: 'لم يتم العثور على متاجر',
        adjustSearchFilter: 'جرب تعديل البحث أو فلتر الفئة',
        noApprovedShops: 'لا توجد متاجر معتمدة متاحة بعد',
        categories: 'الفئات',
        all: 'الكل',
        general: 'عام',
        newShop: 'جديد',
        viewShop: 'عرض المتجر',
        productsCount: 'منتجات',
        rating: 'التقييم',
        shopInfo: 'معلومات المتجر',
        contactShop: 'التواصل مع المتجر',
        shopProducts: 'منتجات المتجر',
        searchProducts: 'البحث في المنتجات...',
        noProductsInShop: 'لم يتم العثور على منتجات',
        adjustProductSearch: 'جرب تعديل البحث أو فلتر الفئة',
        noProductsYet: 'هذا المتجر لا يحتوي على أي منتجات بعد',
        gridView: 'عرض الشبكة',
        listView: 'عرض القائمة',
      },
      products: {
        product: 'منتج',
        products: 'المنتجات',
        price: 'السعر',
        originalPrice: 'السعر الأصلي',
        discount: 'خصم',
        inStock: 'متوفر',
        outOfStock: 'غير متوفر',
        lowStock: 'مخزون منخفض',
        rating: 'التقييم',
        reviews: 'المراجعات',
        addToCart: 'أضف إلى السلة',
        buyNow: 'اشتري الآن',
        description: 'الوصف',
        specifications: 'المواصفات',
        category: 'الفئة',
        vendor: 'التاجر',
        featured: 'مميز',
        bestSelling: 'الأكثر مبيعاً',
        onSale: 'في التخفيضات',
        recommended: 'موصى به',
        addedToCart: 'تمت الإضافة إلى السلة',
        addedToCartMessage: 'تمت إضافته إلى سلتك.',
        continueShopping: 'متابعة التسوق',
        viewCart: 'عرض السلة',
        failedToAddToCart: 'فشل في إضافة المنتج إلى السلة. يرجى المحاولة مرة أخرى.',
        available: 'متوفر',
        currentlyUnavailable: 'غير متوفر حالياً',
        readMore: 'قراءة المزيد',
        readLess: 'قراءة أقل',
        tags: 'العلامات',
        productsCount: 'منتجات',
        // Product management
        addProduct: 'إضافة منتج',
        editProduct: 'تعديل المنتج',
        updateProduct: 'تحديث المنتج',
        productName: 'اسم المنتج',
        productDescription: 'وصف المنتج',
        productPrice: 'سعر المنتج',
        productOriginalPrice: 'السعر الأصلي (اختياري)',
        productCategory: 'فئة المنتج',
        productInventory: 'المخزون',
        productTags: 'العلامات (اختياري)',
        productImages: 'صور المنتج',
        selectCategory: 'اختر الفئة',
        basicInformation: 'المعلومات الأساسية',
        pricingInventory: 'التسعير والمخزون',
        additionalDetails: 'تفاصيل إضافية',
        productAddedSuccessfully: 'تمت إضافة المنتج بنجاح!',
        productUpdatedSuccessfully: 'تم تحديث المنتج بنجاح!',
        failedToAddProduct: 'فشل في إضافة المنتج. يرجى المحاولة مرة أخرى.',
        failedToUpdateProduct: 'فشل في تحديث المنتج. يرجى المحاولة مرة أخرى.',
        productNamePlaceholder: 'أدخل اسم المنتج',
        productDescriptionPlaceholder: 'اوصف منتجك...',
        productPricePlaceholder: 'أدخل السعر بالدينار البحريني',
        productOriginalPricePlaceholder: 'أدخل السعر الأصلي (إذا كان في التخفيضات)',
        productInventoryPlaceholder: 'أدخل الكمية المتوفرة',
        productTagsPlaceholder: 'مثال: لاسلكي، بلوتوث، مميز',
        isActive: 'نشط',
        productStatus: 'حالة المنتج',
      },
      cart: {
        cart: 'السلة',
        addToCart: 'أضف إلى السلة',
        removeFromCart: 'إزالة من السلة',
        quantity: 'الكمية',
        subtotal: 'المجموع الفرعي',
        tax: 'الضريبة',
        shipping: 'الشحن',
        total: 'المجموع',
        checkout: 'الدفع',
        proceedToCheckout: 'متابعة إلى الدفع',
        emptyCart: 'إفراغ السلة',
        cartIsEmpty: 'سلتك فارغة',
        continueShopping: 'متابعة التسوق',
        removeItem: 'إزالة العنصر',
        removeItemConfirmation: 'هل أنت متأكد من إزالة هذا العنصر من سلتك؟',
        updateCart: 'تحديث السلة',
        shoppingCart: 'سلة التسوق',
        clearAll: 'مسح الكل',
        clearCartConfirmation: 'هل أنت متأكد من إزالة جميع العناصر من سلتك؟',
        itemsInCart: 'عناصر في سلتك',
        orderSummary: 'ملخص الطلب',
        free: 'مجاني',
        calculatedAtCheckout: 'يُحسب عند الدفع',
        startShopping: 'ابدأ التسوق',
        addSomeProducts: 'أضف بعض المنتجات للبدء',
      },
      checkout: {
        checkout: 'الدفع',
        shippingAddress: 'عنوان الشحن',
        paymentInformation: 'معلومات الدفع',
        review: 'مراجعة',
        streetAddress: 'عنوان الشارع',
        city: 'المدينة',
        state: 'الولاية',
        zipCode: 'الرمز البريدي',
        country: 'البلد',
        paymentMethod: 'طريقة الدفع',
        creditCard: 'بطاقة ائتمان',
        paypal: 'PayPal',
        applePay: 'Apple Pay',
        googlePay: 'Google Pay',
        bankTransfer: 'تحويل بنكي',
        buyNowPayLater: 'اشتري الآن، ادفع لاحقاً',
        cardNumber: 'رقم البطاقة',
        expiryDate: 'تاريخ الانتهاء',
        cvv: 'رمز الأمان',
        cardholderName: 'اسم حامل البطاقة',
        placeOrder: 'تأكيد الطلب',
        orderPlaced: 'تم تأكيد الطلب',
        orderConfirmation: 'تأكيد الطلب',
        thankYou: 'شكراً لك على طلبك!',
        orderNumber: 'رقم الطلب',
        estimatedDelivery: 'التسليم المتوقع',
        trackOrder: 'تتبع الطلب',
        continueShoppingAfterOrder: 'متابعة التسوق',
        emptyCartTitle: 'سلتك فارغة',
        emptyCartDescription: 'أضف بعض العناصر إلى سلتك قبل الدفع',
        goToShop: 'اذهب إلى المتجر',
        loginRequired: 'يرجى تسجيل الدخول لتأكيد الطلب',
        cartIssues: 'مشاكل في السلة',
        cartValidationError: 'بعض العناصر في سلتك لم تعد متاحة. يرجى مراجعة سلتك.',
        completeRequiredFields: 'يرجى إكمال جميع الحقول المطلوبة',
        processingPayment: 'جاري معالجة الدفع...',
        paymentFailed: 'فشل الدفع. يرجى المحاولة مرة أخرى.',
        paymentSuccess: 'تم الدفع بنجاح!',
        edit: 'تعديل',
      },
      orders: {
        order: 'طلب',
        orders: 'الطلبات',
        orderHistory: 'تاريخ الطلبات',
        orderDetails: 'تفاصيل الطلب',
        orderNumber: 'رقم الطلب',
        orderDate: 'تاريخ الطلب',
        orderStatus: 'حالة الطلب',
        pending: 'قيد الانتظار',
        confirmed: 'مؤكد',
        shipped: 'تم الشحن',
        delivered: 'تم التسليم',
        cancelled: 'ملغي',
        refunded: 'مسترد',
        trackOrder: 'تتبع الطلب',
        reorder: 'إعادة الطلب',
        // Additional order details
        orderPlaced: 'تم وضع الطلب',
        orderItems: 'عناصر الطلب',
        shippingInformation: 'معلومات الشحن',
        deliveryAddress: 'عنوان التسليم',
        reorderItems: 'إعادة طلب العناصر',
        addAllItemsToCart: 'إضافة جميع العناصر من هذا الطلب إلى سلتك؟',
        itemsAddedToCart: 'تمت إضافة العناصر إلى السلة!',
        orderNotFound: 'الطلب غير موجود',
        orderNotFoundDescription: 'الطلب الذي تبحث عنه غير موجود أو تم حذفه.',
        leaveReview: 'اترك مراجعة',
        featureComingSoon: 'الميزة قادمة قريباً',
        reviewFunctionalityComingSoon: 'ستكون وظيفة المراجعة متاحة قريباً!',
        noOrdersFound: 'لم يتم العثور على طلبات',
      },
      errors: {
        productNotFound: 'المنتج غير موجود',
        productNotFoundDescription: 'المنتج الذي تبحث عنه غير موجود أو تم حذفه.',
        goBack: 'العودة',
        networkError: 'خطأ في الشبكة',
        tryAgain: 'حاول مرة أخرى',
        somethingWentWrong: 'حدث خطأ ما',
      },
      demo: {
        demoCredentials: 'بيانات تجريبية',
        fillDemoCredentials: 'ملء البيانات التجريبية',
        adminCredentials: 'بيانات المدير',
        userCredentials: 'بيانات المستخدم',
      },
      success: {
        loginSuccess: 'تم تسجيل الدخول بنجاح!',
        registrationSuccess: 'تم التسجيل بنجاح!',
        vendorApproved: 'تم قبول التاجر بنجاح!',
        vendorRejected: 'تم رفض التاجر بنجاح!',
        productCreated: 'تم إنشاء المنتج بنجاح!',
        productUpdated: 'تم تحديث المنتج بنجاح!',
        orderPlaced: 'تم تقديم الطلب بنجاح!',
        orderUpdated: 'تم تحديث حالة الطلب بنجاح!',
      },
    };

  }
}

export default I18nService.getInstance();