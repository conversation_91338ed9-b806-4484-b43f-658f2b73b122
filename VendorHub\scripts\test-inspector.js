#!/usr/bin/env node

/**
 * Test script for Arabic Translation Inspector
 * 
 * This script runs the inspector and validates its functionality
 * without requiring the full codebase to be present.
 */

const fs = require('fs');
const path = require('path');

// Mock data for testing
const mockI18nService = `
interface TranslationKeys {
  common: {
    welcome: string;
    hello: string;
  };
  auth: {
    login: string;
    register: string;
  };
}

export class I18nService {
  private translations = {
    en: {
      common: {
        welcome: 'Welcome',
        hello: 'Hello',
      },
      auth: {
        login: 'Login',
        register: 'Register',
      },
    },
    ar: {
      common: {
        welcome: 'أهلاً وسهلاً',
        hello: 'مرحبا',
      },
      auth: {
        login: 'تسجيل الدخول',
        register: 'إنشاء حساب',
      },
    },
  };
}
`;

const mockScreenGood = `
import React from 'react';
import { useI18n } from '../../hooks';
import { RTLView, RTLText, RTLIcon } from '../../components/RTL';

export const TestScreen = () => {
  const { t } = useI18n();
  
  return (
    <RTLView>
      <RTLText>{t('common.welcome')}</RTLText>
      <RTLIcon name="home" />
    </RTLView>
  );
};
`;

const mockScreenBad = `
import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export const BadScreen = () => {
  return (
    <View>
      <Text>Hardcoded text here</Text>
      <Ionicons name="home" />
    </View>
  );
};
`;

async function createTestEnvironment() {
  console.log('🧪 Creating test environment...');
  
  // Create test directories
  const testDirs = [
    'src/services',
    'src/screens/test',
    'src/components/RTL'
  ];
  
  for (const dir of testDirs) {
    const fullPath = path.join(__dirname, '..', dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
  }
  
  // Create mock files
  const testFiles = [
    {
      path: 'src/services/I18nService.ts',
      content: mockI18nService
    },
    {
      path: 'src/screens/test/GoodScreen.tsx',
      content: mockScreenGood
    },
    {
      path: 'src/screens/test/BadScreen.tsx',
      content: mockScreenBad
    }
  ];
  
  for (const file of testFiles) {
    const fullPath = path.join(__dirname, '..', file.path);
    fs.writeFileSync(fullPath, file.content);
  }
  
  console.log('✅ Test environment created');
}

async function cleanupTestEnvironment() {
  console.log('🧹 Cleaning up test environment...');
  
  const testFiles = [
    'src/services/I18nService.ts',
    'src/screens/test/GoodScreen.tsx',
    'src/screens/test/BadScreen.tsx'
  ];
  
  for (const file of testFiles) {
    const fullPath = path.join(__dirname, '..', file.path);
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
    }
  }
  
  // Remove test directories if empty
  const testDirs = [
    'src/screens/test',
    'src/services',
    'src/components/RTL'
  ];
  
  for (const dir of testDirs) {
    const fullPath = path.join(__dirname, '..', dir);
    try {
      if (fs.existsSync(fullPath)) {
        const files = fs.readdirSync(fullPath);
        if (files.length === 0) {
          fs.rmdirSync(fullPath);
        }
      }
    } catch (error) {
      // Directory not empty or other error, ignore
    }
  }
  
  console.log('✅ Test environment cleaned up');
}

async function runTest() {
  console.log('🚀 Testing Arabic Translation Inspector\n');
  
  try {
    // Create test environment
    await createTestEnvironment();
    
    // Import and run inspector
    const { ArabicTranslationInspector } = require('./arabic-translation-inspector');
    
    console.log('📊 Running inspector...\n');
    const inspector = new ArabicTranslationInspector();
    await inspector.inspect();
    
    // Validate results
    console.log('\n🔍 Validating results...');
    
    const results = inspector.results;
    
    // Check if translation analysis worked
    if (results.translations.totalKeys > 0) {
      console.log('✅ Translation analysis working');
    } else {
      console.log('❌ Translation analysis failed');
    }
    
    // Check if file analysis worked
    if (results.summary.totalFiles > 0) {
      console.log('✅ File analysis working');
    } else {
      console.log('❌ File analysis failed');
    }
    
    // Check if issues were detected
    if (results.summary.issues.length > 0) {
      console.log('✅ Issue detection working');
    } else {
      console.log('⚠️  No issues detected (might be expected)');
    }
    
    console.log('\n📋 Test Results Summary:');
    console.log(`- Files analyzed: ${results.summary.totalFiles}`);
    console.log(`- Translation keys: ${results.translations.totalKeys}`);
    console.log(`- Issues found: ${results.summary.issues.length}`);
    console.log(`- Translation coverage: ${results.translations.coverage}%`);
    console.log(`- RTL coverage: ${results.rtlUsage.coverage}%`);
    
    console.log('\n✅ Inspector test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    // Always cleanup
    await cleanupTestEnvironment();
  }
}

// Run test if called directly
if (require.main === module) {
  runTest();
}

module.exports = { runTest };
