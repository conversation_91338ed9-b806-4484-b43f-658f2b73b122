import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme, useI18n } from '../hooks';
import { COLORS } from '../constants/theme';

import { AdminDashboardScreen } from '../screens/admin/AdminDashboardScreen';
import { VendorManagementScreen } from '../screens/admin/VendorManagementScreen';
import { ProductOverviewScreen } from '../screens/admin/ProductOverviewScreen';
import { OrderManagementScreen } from '../screens/admin/OrderManagementScreen';

export type AdminTabParamList = {
  Dashboard: undefined;
  Vendors: undefined;
  Products: undefined;
  Orders: undefined;
};

export type AdminStackParamList = {
  AdminTabs: undefined;
  VendorDetails: { vendorId: string };
  ProductDetails: { productId: string };
  OrderDetails: { orderId: string };
};

const Tab = createBottomTabNavigator<AdminTabParamList>();
const Stack = createStackNavigator<AdminStackParamList>();

const AdminTabs: React.FC = () => {
  const { colors } = useTheme();
  const { t } = useI18n();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'analytics' : 'analytics-outline';
              break;
            case 'Vendors':
              iconName = focused ? 'storefront' : 'storefront-outline';
              break;
            case 'Products':
              iconName = focused ? 'cube' : 'cube-outline';
              break;
            case 'Orders':
              iconName = focused ? 'receipt' : 'receipt-outline';
              break;
            default:
              iconName = 'circle-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerStyle: {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        headerTintColor: colors.textPrimary,
        headerTitleStyle: {
          fontWeight: '600',
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={AdminDashboardScreen}
        options={{ title: t('nav.dashboard') }}
      />
      <Tab.Screen
        name="Vendors"
        component={VendorManagementScreen}
        options={{ title: t('nav.vendors') }}
      />
      <Tab.Screen
        name="Products"
        component={ProductOverviewScreen}
        options={{ title: t('nav.products') }}
      />
      <Tab.Screen
        name="Orders"
        component={OrderManagementScreen}
        options={{ title: t('nav.orders') }}
      />
    </Tab.Navigator>
  );
};

export const AdminNavigator: React.FC = () => {
  const { colors } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        },
        headerTintColor: colors.textPrimary,
        headerTitleStyle: {
          fontWeight: '600',
        },
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen 
        name="AdminTabs" 
        component={AdminTabs}
        options={{ headerShown: false }}
      />
      {/* Additional stack screens will be added here */}
    </Stack.Navigator>
  );
};
