import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { useThemedStyles, useI18n } from '../hooks';
import { Card } from './Card';
import { Button } from './Button';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  PRODUCT_CATEGORIES,
} from '../constants/theme';
import { formatCurrency } from '../utils';
import type { ThemeColors } from '../contexts/ThemeContext';
import type { ProductCategory } from '../constants';

export interface AdvancedFilterOptions {
  categories: ProductCategory[];
  priceRange: { min: number; max: number };
  rating: number;
  inStock: boolean;
  onSale: boolean;
  freeShipping: boolean;
  vendors: string[];
  sortBy: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'newest' | 'popularity' | 'discount';
  availability: 'all' | 'in_stock' | 'low_stock' | 'out_of_stock';
  condition: 'all' | 'new' | 'refurbished' | 'used';
}

interface AdvancedFilterPanelProps {
  visible: boolean;
  onClose: () => void;
  filters: AdvancedFilterOptions;
  onFiltersChange: (filters: AdvancedFilterOptions) => void;
  priceRange: { min: number; max: number };
  availableVendors: Array<{ id: string; name: string; productCount: number }>;
  onApply: () => void;
  onReset: () => void;
}

export const AdvancedFilterPanel: React.FC<AdvancedFilterPanelProps> = ({
  visible,
  onClose,
  filters,
  onFiltersChange,
  priceRange,
  availableVendors,
  onApply,
  onReset,
}) => {
  const styles = useThemedStyles(createStyles);
  const { t } = useI18n();
  const [tempFilters, setTempFilters] = useState<AdvancedFilterOptions>(filters);

  const updateTempFilters = (updates: Partial<AdvancedFilterOptions>) => {
    setTempFilters(prev => ({ ...prev, ...updates }));
  };

  const handleApply = () => {
    onFiltersChange(tempFilters);
    onApply();
    onClose();
  };

  const handleReset = () => {
    const resetFilters: AdvancedFilterOptions = {
      categories: [],
      priceRange: { min: priceRange.min, max: priceRange.max },
      rating: 0,
      inStock: false,
      onSale: false,
      freeShipping: false,
      vendors: [],
      sortBy: 'relevance',
      availability: 'all',
      condition: 'all',
    };
    setTempFilters(resetFilters);
    onFiltersChange(resetFilters);
    onReset();
  };

  const categoryOptions = Object.values(PRODUCT_CATEGORIES);
  const sortOptions = [
    { key: 'relevance', label: t('search.relevance'), icon: 'search-outline' },
    { key: 'price_low', label: t('search.priceLowToHigh'), icon: 'arrow-up-outline' },
    { key: 'price_high', label: t('search.priceHighToLow'), icon: 'arrow-down-outline' },
    { key: 'rating', label: t('search.customerRating'), icon: 'star-outline' },
    { key: 'newest', label: t('search.newestFirst'), icon: 'time-outline' },
    { key: 'popularity', label: t('search.mostPopular'), icon: 'trending-up-outline' },
    { key: 'discount', label: t('search.biggestDiscount'), icon: 'pricetag-outline' },
  ];

  const availabilityOptions = [
    { key: 'all', label: t('search.allProducts') },
    { key: 'in_stock', label: t('search.inStock') },
    { key: 'low_stock', label: t('search.lowStock') },
    { key: 'out_of_stock', label: t('search.outOfStock') },
  ];

  const conditionOptions = [
    { key: 'all', label: t('search.allConditions') },
    { key: 'new', label: t('search.new') },
    { key: 'refurbished', label: t('search.refurbished') },
    { key: 'used', label: t('search.used') },
  ];

  const renderSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderCategoryFilter = () => (
    renderSection(t('search.categories'), (
      <View style={styles.categoryGrid}>
        {categoryOptions.map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryChip,
              tempFilters.categories.includes(category) && styles.categoryChipActive,
            ]}
            onPress={() => {
              const newCategories = tempFilters.categories.includes(category)
                ? tempFilters.categories.filter(c => c !== category)
                : [...tempFilters.categories, category];
              updateTempFilters({ categories: newCategories });
            }}
          >
            <Text
              style={[
                styles.categoryChipText,
                tempFilters.categories.includes(category) && styles.categoryChipTextActive,
              ]}
            >
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    ))
  );

  const renderPriceFilter = () => (
    renderSection(t('search.priceRange'), (
      <View style={styles.priceContainer}>
        <View style={styles.priceLabels}>
          <Text style={styles.priceLabel}>
            {formatCurrency(tempFilters.priceRange.min)}
          </Text>
          <Text style={styles.priceLabel}>
            {formatCurrency(tempFilters.priceRange.max)}
          </Text>
        </View>
        <View style={styles.sliderContainer}>
          <Text style={styles.sliderLabel}>Min</Text>
          <Slider
            style={styles.slider}
            minimumValue={priceRange.min}
            maximumValue={priceRange.max}
            value={tempFilters.priceRange.min}
            onValueChange={(value) => 
              updateTempFilters({ 
                priceRange: { ...tempFilters.priceRange, min: Math.round(value) }
              })
            }
            minimumTrackTintColor="#667eea"
            maximumTrackTintColor="#CCCCCC"
            thumbStyle={styles.sliderThumb}
          />
        </View>
        <View style={styles.sliderContainer}>
          <Text style={styles.sliderLabel}>Max</Text>
          <Slider
            style={styles.slider}
            minimumValue={priceRange.min}
            maximumValue={priceRange.max}
            value={tempFilters.priceRange.max}
            onValueChange={(value) => 
              updateTempFilters({ 
                priceRange: { ...tempFilters.priceRange, max: Math.round(value) }
              })
            }
            minimumTrackTintColor="#667eea"
            maximumTrackTintColor="#CCCCCC"
            thumbStyle={styles.sliderThumb}
          />
        </View>
      </View>
    ))
  );

  const renderRatingFilter = () => (
    renderSection(t('search.minimumRating'), (
      <View style={styles.ratingContainer}>
        {[1, 2, 3, 4, 5].map((rating) => (
          <TouchableOpacity
            key={rating}
            style={[
              styles.ratingOption,
              tempFilters.rating >= rating && styles.ratingOptionActive,
            ]}
            onPress={() => updateTempFilters({ rating: rating === tempFilters.rating ? 0 : rating })}
          >
            <Ionicons 
              name={tempFilters.rating >= rating ? "star" : "star-outline"} 
              size={24} 
              color={tempFilters.rating >= rating ? "#FFD700" : "#CCCCCC"} 
            />
          </TouchableOpacity>
        ))}
        <Text style={styles.ratingText}>
          {tempFilters.rating > 0 ? `${tempFilters.rating}${t('search.starsAndUp')}` : t('search.anyRating')}
        </Text>
      </View>
    ))
  );

  const renderToggleFilters = () => (
    renderSection(t('search.quickFilters'), (
      <View style={styles.toggleContainer}>
        <View style={styles.toggleRow}>
          <Text style={styles.toggleLabel}>In Stock Only</Text>
          <Switch
            value={tempFilters.inStock}
            onValueChange={(value) => updateTempFilters({ inStock: value })}
            trackColor={{ false: '#CCCCCC', true: '#667eea' }}
            thumbColor="#FFFFFF"
          />
        </View>
        <View style={styles.toggleRow}>
          <Text style={styles.toggleLabel}>On Sale</Text>
          <Switch
            value={tempFilters.onSale}
            onValueChange={(value) => updateTempFilters({ onSale: value })}
            trackColor={{ false: '#CCCCCC', true: '#667eea' }}
            thumbColor="#FFFFFF"
          />
        </View>
        <View style={styles.toggleRow}>
          <Text style={styles.toggleLabel}>Free Shipping</Text>
          <Switch
            value={tempFilters.freeShipping}
            onValueChange={(value) => updateTempFilters({ freeShipping: value })}
            trackColor={{ false: '#CCCCCC', true: '#667eea' }}
            thumbColor="#FFFFFF"
          />
        </View>
      </View>
    ))
  );

  const renderSortOptions = () => (
    renderSection(t('search.sortBy'), (
      <View style={styles.sortContainer}>
        {sortOptions.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.sortOption,
              tempFilters.sortBy === option.key && styles.sortOptionActive,
            ]}
            onPress={() => updateTempFilters({ sortBy: option.key as any })}
          >
            <Ionicons 
              name={option.icon as any} 
              size={20} 
              color={tempFilters.sortBy === option.key ? "#667eea" : "#666"} 
            />
            <Text
              style={[
                styles.sortOptionText,
                tempFilters.sortBy === option.key && styles.sortOptionTextActive,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    ))
  );

  const renderVendorFilter = () => (
    renderSection(t('search.vendors'), (
      <View style={styles.vendorContainer}>
        {availableVendors.slice(0, 10).map((vendor) => (
          <TouchableOpacity
            key={vendor.id}
            style={[
              styles.vendorOption,
              tempFilters.vendors.includes(vendor.id) && styles.vendorOptionActive,
            ]}
            onPress={() => {
              const newVendors = tempFilters.vendors.includes(vendor.id)
                ? tempFilters.vendors.filter(v => v !== vendor.id)
                : [...tempFilters.vendors, vendor.id];
              updateTempFilters({ vendors: newVendors });
            }}
          >
            <Text
              style={[
                styles.vendorOptionText,
                tempFilters.vendors.includes(vendor.id) && styles.vendorOptionTextActive,
              ]}
            >
              {vendor.name}
            </Text>
            <Text style={styles.vendorProductCount}>
              {vendor.productCount} products
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    ))
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
          <Text style={styles.title}>Advanced Filters</Text>
          <TouchableOpacity onPress={handleReset} style={styles.resetButton}>
            <Text style={styles.resetText}>Reset</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderCategoryFilter()}
          {renderPriceFilter()}
          {renderRatingFilter()}
          {renderToggleFilters()}
          {renderSortOptions()}
          {renderVendorFilter()}
        </ScrollView>

        <View style={styles.footer}>
          <Button
            title="Apply Filters"
            onPress={handleApply}
            style={styles.applyButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  closeButton: {
    padding: SPACING.sm,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  resetButton: {
    padding: SPACING.sm,
  },
  resetText: {
    fontSize: FONT_SIZES.md,
    color: '#667eea',
    fontWeight: FONT_WEIGHTS.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  section: {
    marginVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.md,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.sm,
  },
  categoryChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  categoryChipActive: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  categoryChipText: {
    fontSize: FONT_SIZES.sm,
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  categoryChipTextActive: {
    color: '#FFFFFF',
  },
  priceContainer: {
    gap: SPACING.md,
  },
  priceLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priceLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#667eea',
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  sliderLabel: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    width: 30,
  },
  slider: {
    flex: 1,
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#667eea',
    width: 20,
    height: 20,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  ratingOption: {
    padding: SPACING.xs,
  },
  ratingOptionActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderRadius: BORDER_RADIUS.sm,
  },
  ratingText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginLeft: SPACING.md,
  },
  toggleContainer: {
    gap: SPACING.md,
  },
  toggleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.sm,
  },
  toggleLabel: {
    fontSize: FONT_SIZES.md,
    color: colors.text,
  },
  sortContainer: {
    gap: SPACING.sm,
  },
  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
    gap: SPACING.md,
  },
  sortOptionActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderWidth: 1,
    borderColor: '#667eea',
  },
  sortOptionText: {
    fontSize: FONT_SIZES.md,
    color: colors.text,
  },
  sortOptionTextActive: {
    color: '#667eea',
    fontWeight: FONT_WEIGHTS.medium,
  },
  vendorContainer: {
    gap: SPACING.sm,
  },
  vendorOption: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  vendorOptionActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderColor: '#667eea',
  },
  vendorOptionText: {
    fontSize: FONT_SIZES.md,
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  vendorOptionTextActive: {
    color: '#667eea',
  },
  vendorProductCount: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginTop: SPACING.xs,
  },
  footer: {
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  applyButton: {
    marginBottom: 0,
  },
});
