#!/usr/bin/env node

/**
 * Hardcoded String Detector and Fixer for VendorHub
 * 
 * This script finds hardcoded English strings in React Native TypeScript files
 * and suggests translation keys or fixes them automatically.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Hardcoded String Detector for VendorHub');
console.log('==========================================\n');

// Configuration
const CONFIG = {
  srcDir: path.join(__dirname, '..', 'src'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /build/,
    /dist/,
    /coverage/,
    /\.test\./,
    /\.spec\./,
    /I18nService\.ts$/ // Don't scan the translation service itself
  ],
  fileExtensions: ['.tsx', '.ts']
};

// Patterns to exclude (these are not translatable strings)
const EXCLUDE_PATTERNS = [
  // Function calls and imports
  /t\(/,
  /useI18n/,
  /import/,
  /export/,
  /console\./,
  /\.log/,
  /\.error/,
  /\.warn/,
  /\.info/,
  
  // JSX attributes that shouldn't be translated
  /name=/,
  /key=/,
  /testID=/,
  /accessibilityLabel=/,
  /source=/,
  /uri=/,
  /style=/,
  /onPress=/,
  /onChange=/,
  /placeholder=/,
  
  // Technical strings
  /http/,
  /https/,
  /www\./,
  /\.com/,
  /\.org/,
  /\.net/,
  /api\//,
  
  // File paths and extensions
  /\.\w+$/,
  /\/\w+/,
  
  // Color codes and CSS
  /#[0-9a-fA-F]{3,6}/,
  /rgba?\(/,
  /px|em|rem|%/,
  
  // Numbers and technical values
  /^\d+$/,
  /^\d+\.\d+$/,
  /version/i,
  
  // Common technical terms that shouldn't be translated
  /flex/i,
  /center/i,
  /left/i,
  /right/i,
  /top/i,
  /bottom/i,
  /absolute/i,
  /relative/i,
  /hidden/i,
  /visible/i,
  /auto/i,
  /none/i,
  /bold/i,
  /normal/i,
  /italic/i,
];

// Common hardcoded strings and their suggested translation keys
const COMMON_TRANSLATIONS = {
  'Loading...': 'common.loading',
  'Error': 'common.error',
  'Success': 'common.success',
  'Cancel': 'common.cancel',
  'Confirm': 'common.confirm',
  'Save': 'common.save',
  'Delete': 'common.delete',
  'Edit': 'common.edit',
  'Add': 'common.add',
  'Search': 'common.search',
  'Filter': 'common.filter',
  'Sort': 'common.sort',
  'Refresh': 'common.refresh',
  'Back': 'common.back',
  'Next': 'common.next',
  'Done': 'common.done',
  'Close': 'common.close',
  'Yes': 'common.yes',
  'No': 'common.no',
  
  // Navigation
  'Home': 'nav.home',
  'Profile': 'nav.profile',
  'Dashboard': 'nav.dashboard',
  'Products': 'nav.products',
  'Orders': 'nav.orders',
  'Cart': 'nav.cart',
  'Shops': 'nav.shops',
  'Messages': 'nav.messages',
  
  // Auth
  'Login': 'auth.login',
  'Register': 'auth.register',
  'Email': 'auth.email',
  'Password': 'auth.password',
  'Welcome': 'auth.welcome',
  
  // Common UI
  'View All': 'common.viewAll',
  'See All': 'common.seeAll',
  'Try Again': 'common.tryAgain',
  'Something went wrong': 'common.somethingWentWrong',
};

class HardcodedStringDetector {
  constructor() {
    this.results = {
      totalFiles: 0,
      filesWithIssues: 0,
      totalIssues: 0,
      issues: [],
      suggestions: []
    };
  }

  /**
   * Main detection process
   */
  async detect() {
    console.log('🔍 Scanning for hardcoded strings...\n');
    
    await this.scanDirectory(CONFIG.srcDir);
    
    this.printResults();
  }

  /**
   * Recursively scan directory for TypeScript files
   */
  async scanDirectory(dirPath) {
    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        // Skip excluded patterns
        if (this.shouldExclude(fullPath)) {
          continue;
        }
        
        if (entry.isDirectory()) {
          await this.scanDirectory(fullPath);
        } else if (entry.isFile() && this.isTargetFile(entry.name)) {
          await this.processFile(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error.message);
    }
  }

  /**
   * Check if path should be excluded
   */
  shouldExclude(filePath) {
    const relativePath = path.relative(path.join(__dirname, '..'), filePath);
    
    for (const pattern of CONFIG.excludePatterns) {
      if (pattern.test(relativePath)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Check if file is a target TypeScript file
   */
  isTargetFile(fileName) {
    return CONFIG.fileExtensions.some(ext => fileName.endsWith(ext));
  }

  /**
   * Process individual file
   */
  async processFile(filePath) {
    try {
      this.results.totalFiles++;
      
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(path.join(__dirname, '..'), filePath);
      
      const issues = this.findHardcodedStrings(content, relativePath);
      
      if (issues.length > 0) {
        this.results.filesWithIssues++;
        this.results.totalIssues += issues.length;
        this.results.issues.push({
          file: relativePath,
          issues: issues
        });
        
        console.log(`📄 ${relativePath} - ${issues.length} issue(s) found`);
      }
      
    } catch (error) {
      console.error(`Error processing file ${filePath}:`, error.message);
    }
  }

  /**
   * Find hardcoded strings in file content
   */
  findHardcodedStrings(content, filePath) {
    const issues = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // Skip lines that are comments
      if (line.trim().startsWith('//') || line.trim().startsWith('/*') || line.trim().startsWith('*')) {
        return;
      }
      
      // Find string literals
      const stringMatches = line.matchAll(/['"`]([^'"`\n]{2,}?)['"`]/g);
      
      for (const match of stringMatches) {
        const fullMatch = match[0];
        const stringContent = match[1];
        
        // Skip if it matches exclude patterns
        if (this.shouldExcludeString(line, stringContent)) {
          continue;
        }
        
        // Check if it looks like a translatable string
        if (this.isTranslatableString(stringContent)) {
          const suggestion = COMMON_TRANSLATIONS[stringContent] || this.suggestTranslationKey(stringContent);
          
          issues.push({
            line: index + 1,
            content: line.trim(),
            string: stringContent,
            fullMatch: fullMatch,
            suggestion: suggestion
          });
        }
      }
    });
    
    return issues;
  }

  /**
   * Check if string should be excluded from translation
   */
  shouldExcludeString(line, stringContent) {
    // Check if line contains exclude patterns
    for (const pattern of EXCLUDE_PATTERNS) {
      if (pattern.test(line)) {
        return true;
      }
    }
    
    // Skip very short strings
    if (stringContent.length < 3) {
      return true;
    }
    
    // Skip strings that are all numbers
    if (/^\d+$/.test(stringContent)) {
      return true;
    }
    
    // Skip strings that look like technical identifiers
    if (/^[a-z][a-zA-Z0-9_-]*$/.test(stringContent) && stringContent.length < 15) {
      return true;
    }
    
    // Skip strings that are single words and look technical
    if (!/\s/.test(stringContent) && stringContent.length < 8) {
      return true;
    }
    
    return false;
  }

  /**
   * Check if string looks translatable
   */
  isTranslatableString(stringContent) {
    // Must contain at least one letter
    if (!/[a-zA-Z]/.test(stringContent)) {
      return false;
    }
    
    // Should be meaningful text (contains spaces or is a known UI term)
    if (stringContent.includes(' ') || stringContent.length > 10) {
      return true;
    }
    
    // Check if it's a known UI term
    const uiTerms = [
      'Home', 'Profile', 'Dashboard', 'Products', 'Orders', 'Cart', 'Shops', 'Messages',
      'Login', 'Register', 'Email', 'Password', 'Welcome', 'Loading', 'Error', 'Success',
      'Cancel', 'Confirm', 'Save', 'Delete', 'Edit', 'Add', 'Search', 'Filter', 'Sort',
      'Refresh', 'Back', 'Next', 'Done', 'Close', 'Yes', 'No', 'Overview', 'Analytics'
    ];
    
    return uiTerms.includes(stringContent);
  }

  /**
   * Suggest translation key for a string
   */
  suggestTranslationKey(stringContent) {
    // Convert to camelCase and suggest a key
    const camelCase = stringContent
      .toLowerCase()
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+(.)/g, (_, char) => char.toUpperCase())
      .replace(/\s/g, '');
    
    // Suggest category based on common patterns
    if (/login|register|password|email|auth/i.test(stringContent)) {
      return `auth.${camelCase}`;
    } else if (/home|product|shop|cart|order/i.test(stringContent)) {
      return `${stringContent.toLowerCase().split(' ')[0]}.${camelCase}`;
    } else {
      return `common.${camelCase}`;
    }
  }

  /**
   * Print detection results
   */
  printResults() {
    console.log('\n🎯 HARDCODED STRING DETECTION RESULTS');
    console.log('====================================');
    console.log(`📊 Total files scanned: ${this.results.totalFiles}`);
    console.log(`📄 Files with issues: ${this.results.filesWithIssues}`);
    console.log(`⚠️  Total issues found: ${this.results.totalIssues}\n`);
    
    if (this.results.issues.length > 0) {
      console.log('📝 Issues Found:');
      console.log('================');
      
      this.results.issues.slice(0, 10).forEach(fileIssue => {
        console.log(`\n📄 ${fileIssue.file}:`);
        fileIssue.issues.slice(0, 5).forEach(issue => {
          console.log(`   Line ${issue.line}: "${issue.string}"`);
          console.log(`   Suggestion: t('${issue.suggestion}')`);
          console.log(`   Context: ${issue.content}`);
          console.log('');
        });
        
        if (fileIssue.issues.length > 5) {
          console.log(`   ... and ${fileIssue.issues.length - 5} more issues in this file\n`);
        }
      });
      
      if (this.results.issues.length > 10) {
        console.log(`\n... and ${this.results.issues.length - 10} more files with issues\n`);
      }
      
      console.log('💡 Recommendations:');
      console.log('===================');
      console.log('1. Replace hardcoded strings with t() function calls');
      console.log('2. Add missing translation keys to I18nService.ts');
      console.log('3. Import useI18n hook in components that need translations');
      console.log('4. Test the app in both English and Arabic to verify translations');
    } else {
      console.log('🎉 No hardcoded strings found! All text appears to be properly translated.');
    }
  }
}

// Run the detector
const detector = new HardcodedStringDetector();
detector.detect().catch(console.error);
