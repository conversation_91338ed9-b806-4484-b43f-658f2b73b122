#!/usr/bin/env node

/**
 * Hardcoded String Detector and Fixer for VendorHub
 * 
 * This script finds hardcoded English strings in React Native TypeScript files
 * and suggests translation keys or fixes them automatically.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Hardcoded String Detector for VendorHub');
console.log('==========================================\n');

// Configuration
const CONFIG = {
  srcDir: path.join(__dirname, '..', 'src'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /build/,
    /dist/,
    /coverage/,
    /\.test\./,
    /\.spec\./,
    /I18nService\.ts$/ // Don't scan the translation service itself
  ],
  fileExtensions: ['.tsx', '.ts']
};

// Patterns to exclude (these are not translatable strings)
const EXCLUDE_PATTERNS = [
  // Function calls and imports
  /t\(/,
  /useI18n/,
  /import/,
  /export/,
  /console\./,
  /\.log/,
  /\.error/,
  /\.warn/,
  /\.info/,
  
  // JSX attributes that shouldn't be translated
  /name=/,
  /key=/,
  /testID=/,
  /accessibilityLabel=/,
  /source=/,
  /uri=/,
  /style=/,
  /onPress=/,
  /onChange=/,
  /placeholder=/,
  
  // Technical strings
  /http/,
  /https/,
  /www\./,
  /\.com/,
  /\.org/,
  /\.net/,
  /api\//,
  
  // File paths and extensions
  /\.\w+$/,
  /\/\w+/,
  
  // Color codes and CSS
  /#[0-9a-fA-F]{3,6}/,
  /rgba?\(/,
  /px|em|rem|%/,
  
  // Numbers and technical values
  /^\d+$/,
  /^\d+\.\d+$/,
  /version/i,
  
  // Common technical terms that shouldn't be translated
  /flex/i,
  /center/i,
  /left/i,
  /right/i,
  /top/i,
  /bottom/i,
  /absolute/i,
  /relative/i,
  /hidden/i,
  /visible/i,
  /auto/i,
  /none/i,
  /bold/i,
  /normal/i,
  /italic/i,
];

// Common hardcoded strings and their suggested translation keys
const COMMON_TRANSLATIONS = {
  'Loading...': 'common.loading',
  'Error': 'common.error',
  'Success': 'common.success',
  'Cancel': 'common.cancel',
  'Confirm': 'common.confirm',
  'Save': 'common.save',
  'Delete': 'common.delete',
  'Edit': 'common.edit',
  'Add': 'common.add',
  'Search': 'common.search',
  'Filter': 'common.filter',
  'Sort': 'common.sort',
  'Refresh': 'common.refresh',
  'Back': 'common.back',
  'Next': 'common.next',
  'Done': 'common.done',
  'Close': 'common.close',
  'Yes': 'common.yes',
  'No': 'common.no',
  
  // Navigation
  'Home': 'nav.home',
  'Profile': 'nav.profile',
  'Dashboard': 'nav.dashboard',
  'Products': 'nav.products',
  'Orders': 'nav.orders',
  'Cart': 'nav.cart',
  'Shops': 'nav.shops',
  'Messages': 'nav.messages',
  
  // Auth
  'Login': 'auth.login',
  'Register': 'auth.register',
  'Email': 'auth.email',
  'Password': 'auth.password',
  'Welcome': 'auth.welcome',
  
  // Common UI
  'View All': 'common.viewAll',
  'See All': 'common.seeAll',
  'Try Again': 'common.tryAgain',
  'Something went wrong': 'common.somethingWentWrong',
};

class HardcodedStringDetector {
  constructor() {
    this.results = {
      totalFiles: 0,
      filesWithIssues: 0,
      totalIssues: 0,
      issues: [],
      suggestions: []
    };
  }

  /**
   * Main detection process
   */
  async detect() {
    console.log('🔍 Scanning for hardcoded strings...\n');
    
    await this.scanDirectory(CONFIG.srcDir);
    
    this.printResults();
  }

  /**
   * Recursively scan directory for TypeScript files
   */
  async scanDirectory(dirPath) {
    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        // Skip excluded patterns
        if (this.shouldExclude(fullPath)) {
          continue;
        }
        
        if (entry.isDirectory()) {
          await this.scanDirectory(fullPath);
        } else if (entry.isFile() && this.isTargetFile(entry.name)) {
          await this.processFile(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dirPath}:`, error.message);
    }
  }

  /**
   * Check if path should be excluded
   */
  shouldExclude(filePath) {
    const relativePath = path.relative(path.join(__dirname, '..'), filePath);
    
    for (const pattern of CONFIG.excludePatterns) {
      if (pattern.test(relativePath)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Check if file is a target TypeScript file
   */
  isTargetFile(fileName) {
    return CONFIG.fileExtensions.some(ext => fileName.endsWith(ext));
  }

  /**
   * Process individual file
   */
  async processFile(filePath) {
    try {
      this.results.totalFiles++;
      
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(path.join(__dirname, '..'), filePath);
      
      const issues = this.findHardcodedStrings(content, relativePath);
      
      if (issues.length > 0) {
        this.results.filesWithIssues++;
        this.results.totalIssues += issues.length;
        this.results.issues.push({
          file: relativePath,
          issues: issues
        });
        
        console.log(`📄 ${relativePath} - ${issues.length} issue(s) found`);
      }
      
    } catch (error) {
      console.error(`Error processing file ${filePath}:`, error.message);
    }
  }

  /**
   * Find hardcoded strings in file content
   */
  findHardcodedStrings(content, filePath) {
    const issues = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // Skip lines that are comments
      if (line.trim().startsWith('//') || line.trim().startsWith('/*') || line.trim().startsWith('*')) {
        return;
      }
      
      // Find string literals
      const stringMatches = line.matchAll(/['"`]([^'"`\n]{2,}?)['"`]/g);
      
      for (const match of stringMatches) {
        const fullMatch = match[0];
        const stringContent = match[1];
        
        // Skip if it matches exclude patterns
        if (this.shouldExcludeString(line, stringContent)) {
          continue;
        }
        
        // Check if it looks like a translatable string
        if (this.isTranslatableString(stringContent)) {
          const suggestion = COMMON_TRANSLATIONS[stringContent] || this.suggestTranslationKey(stringContent);
          
          issues.push({
            line: index + 1,
            content: line.trim(),
            string: stringContent,
            fullMatch: fullMatch,
            suggestion: suggestion
          });
        }
      }
    });
    
    return issues;
  }

  /**
   * Check if string should be excluded from translation
   */
  shouldExcludeString(line, stringContent) {
    // Check if line contains exclude patterns
    for (const pattern of EXCLUDE_PATTERNS) {
      if (pattern.test(line)) {
        return true;
      }
    }
    
    // Skip very short strings
    if (stringContent.length < 3) {
      return true;
    }
    
    // Skip strings that are all numbers
    if (/^\d+$/.test(stringContent)) {
      return true;
    }
    
    // Skip strings that look like technical identifiers
    if (/^[a-z][a-zA-Z0-9_-]*$/.test(stringContent) && stringContent.length < 15) {
      return true;
    }
    
    // Skip strings that are single words and look technical
    if (!/\s/.test(stringContent) && stringContent.length < 8) {
      return true;
    }
    
    return false;
  }

  /**
   * Check if string looks translatable
   */
  isTranslatableString(stringContent) {
    // Must contain at least one letter
    if (!/[a-zA-Z]/.test(stringContent)) {
      return false;
    }
    
    // Should be meaningful text (contains spaces or is a known UI term)
    if (stringContent.includes(' ') || stringContent.length > 10) {
      return true;
    }
    
    // Check if it's a known UI term
    const uiTerms = [
      'Home', 'Profile', 'Dashboard', 'Products', 'Orders', 'Cart', 'Shops', 'Messages',
      'Login', 'Register', 'Email', 'Password', 'Welcome', 'Loading', 'Error', 'Success',
      'Cancel', 'Confirm', 'Save', 'Delete', 'Edit', 'Add', 'Search', 'Filter', 'Sort',
      'Refresh', 'Back', 'Next', 'Done', 'Close', 'Yes', 'No', 'Overview', 'Analytics'
    ];
    
    return uiTerms.includes(stringContent);
  }

  /**
   * Suggest translation key for a string
   */
  suggestTranslationKey(stringContent) {
    // Convert to camelCase and suggest a key
    const camelCase = stringContent
      .toLowerCase()
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+(.)/g, (_, char) => char.toUpperCase())
      .replace(/\s/g, '');
    
    // Suggest category based on common patterns
    if (/login|register|password|email|auth/i.test(stringContent)) {
      return `auth.${camelCase}`;
    } else if (/home|product|shop|cart|order/i.test(stringContent)) {
      return `${stringContent.toLowerCase().split(' ')[0]}.${camelCase}`;
    } else {
      return `common.${camelCase}`;
    }
  }

  /**
   * Print detection results with enhanced categorization
   */
  printResults() {
    console.log('\n🎯 COMPREHENSIVE TRANSLATION INSPECTION RESULTS');
    console.log('===============================================');
    console.log(`📊 Total files scanned: ${this.results.totalFiles}`);
    console.log(`📄 Files with issues: ${this.results.filesWithIssues}`);
    console.log(`⚠️  Total issues found: ${this.results.totalIssues}\n`);

    if (this.results.issues.length > 0) {
      // Categorize issues by priority
      const categorizedIssues = this.categorizeIssues();

      console.log('🔥 CRITICAL PRIORITY - User-Facing Screens');
      console.log('==========================================');
      this.printCategoryIssues(categorizedIssues.critical);

      console.log('\n⚠️  HIGH PRIORITY - Core Components & Navigation');
      console.log('================================================');
      this.printCategoryIssues(categorizedIssues.high);

      console.log('\n📋 MEDIUM PRIORITY - Forms & Interactions');
      console.log('=========================================');
      this.printCategoryIssues(categorizedIssues.medium);

      console.log('\n🔧 LOW PRIORITY - Services & Utilities');
      console.log('======================================');
      this.printCategoryIssues(categorizedIssues.low);

      console.log('\n📊 SUMMARY BY SCREEN TYPE');
      console.log('=========================');
      this.printScreenTypeSummary();

      console.log('\n💡 ACTIONABLE RECOMMENDATIONS');
      console.log('=============================');
      console.log('1. 🔥 IMMEDIATE: Fix auth screens (login, register) - blocks user onboarding');
      console.log('2. 🔥 IMMEDIATE: Fix checkout flow - blocks transactions');
      console.log('3. ⚠️  HIGH: Fix navigation menus - affects all user journeys');
      console.log('4. ⚠️  HIGH: Fix notification system - critical for user engagement');
      console.log('5. 📋 MEDIUM: Fix form validation messages - improves UX');
      console.log('6. 🔧 LOW: Fix service layer messages - mostly for debugging');

      console.log('\n🎯 NEXT STEPS');
      console.log('=============');
      console.log('1. Start with RegisterScreen.tsx (18 issues) and CheckoutScreen.tsx (18 issues)');
      console.log('2. Fix NotificationCenter.tsx (18 issues) for system-wide notifications');
      console.log('3. Address AdvancedFilterPanel.tsx (21 issues) for search functionality');
      console.log('4. Systematically work through vendor screens for seller experience');
      console.log('5. Test each screen in Arabic after translation to verify RTL layout');
    } else {
      console.log('🎉 No hardcoded strings found! All text appears to be properly translated.');
    }
  }

  /**
   * Categorize issues by priority level
   */
  categorizeIssues() {
    const categories = {
      critical: [], // Auth, checkout, core user flows
      high: [],     // Navigation, notifications, core components
      medium: [],   // Forms, search, vendor features
      low: []       // Services, utilities, testing
    };

    this.results.issues.forEach(fileIssue => {
      const file = fileIssue.file.toLowerCase();

      if (file.includes('auth/') || file.includes('checkout') || file.includes('login') || file.includes('register')) {
        categories.critical.push(fileIssue);
      } else if (file.includes('navigation/') || file.includes('notification') || file.includes('offline') || file.includes('error')) {
        categories.high.push(fileIssue);
      } else if (file.includes('screens/') || file.includes('components/') || file.includes('search') || file.includes('filter')) {
        categories.medium.push(fileIssue);
      } else {
        categories.low.push(fileIssue);
      }
    });

    return categories;
  }

  /**
   * Print issues for a specific category
   */
  printCategoryIssues(categoryIssues) {
    if (categoryIssues.length === 0) {
      console.log('✅ No issues found in this category');
      return;
    }

    categoryIssues.slice(0, 5).forEach(fileIssue => {
      console.log(`📄 ${fileIssue.file} (${fileIssue.issues.length} issues)`);
      fileIssue.issues.slice(0, 3).forEach(issue => {
        console.log(`   • "${issue.string}" → t('${issue.suggestion}')`);
      });
      if (fileIssue.issues.length > 3) {
        console.log(`   ... and ${fileIssue.issues.length - 3} more`);
      }
    });

    if (categoryIssues.length > 5) {
      console.log(`... and ${categoryIssues.length - 5} more files in this category`);
    }
  }

  /**
   * Print summary by screen type
   */
  printScreenTypeSummary() {
    const screenTypes = {
      'Auth Screens': 0,
      'Admin Screens': 0,
      'Vendor Screens': 0,
      'Public Screens': 0,
      'Chat Screens': 0,
      'Components': 0,
      'Services': 0,
      'Navigation': 0
    };

    this.results.issues.forEach(fileIssue => {
      const file = fileIssue.file.toLowerCase();
      const issueCount = fileIssue.issues.length;

      if (file.includes('screens/auth/')) screenTypes['Auth Screens'] += issueCount;
      else if (file.includes('screens/admin/')) screenTypes['Admin Screens'] += issueCount;
      else if (file.includes('screens/vendor/')) screenTypes['Vendor Screens'] += issueCount;
      else if (file.includes('screens/public/')) screenTypes['Public Screens'] += issueCount;
      else if (file.includes('screens/chat/')) screenTypes['Chat Screens'] += issueCount;
      else if (file.includes('components/')) screenTypes['Components'] += issueCount;
      else if (file.includes('services/')) screenTypes['Services'] += issueCount;
      else if (file.includes('navigation/')) screenTypes['Navigation'] += issueCount;
    });

    Object.entries(screenTypes).forEach(([type, count]) => {
      if (count > 0) {
        console.log(`${type}: ${count} issues`);
      }
    });
  }
}

// Run the detector
const detector = new HardcodedStringDetector();
detector.detect().catch(console.error);
