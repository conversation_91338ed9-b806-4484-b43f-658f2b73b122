import React from 'react';
import { StyleSheet, TouchableOpacity, RefreshControl } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useAuth, useProducts, useI18n } from '../../hooks';
import { RTLView, RTLText, RTLScrollView, RTLIcon, RTLSafeAreaView } from '../../components/RTL';
import { Card, Button, StatisticsCounter, EmptyState, RecommendationsSection, LanguageSelector } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
  ICON_SIZES,
} from '../../constants/theme';
import { APP_NAME } from '../../constants';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface HomeScreenProps {
  navigation: any;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user, logout } = useAuth();
  const { getFeaturedProducts, getBestSellingProducts, getProductsOnSale } = useProducts();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = React.useState(false);

  const featuredProducts = getFeaturedProducts(6);
  const bestSellingProducts = getBestSellingProducts(6);
  const saleProducts = getProductsOnSale();

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleLogout = async () => {
    await logout();
  };

  const handleSearchPress = () => {
    navigation.navigate('Search');
  };

  const handleCategoryPress = (category: string) => {
    navigation.navigate('Search', { category });
  };

  const categories = [
    { name: t('home.electronics'), key: 'Electronics', icon: 'phone-portrait-outline', color: '#667eea' },
    { name: t('home.fashion'), key: 'Fashion', icon: 'shirt-outline', color: '#764ba2' },
    { name: t('home.homeGarden'), key: 'Home & Garden', icon: 'home-outline', color: '#4CAF50' },
    { name: t('home.sports'), key: 'Sports', icon: 'fitness-outline', color: '#FF9800' },
    { name: t('home.books'), key: 'Books', icon: 'book-outline', color: '#2196F3' },
    { name: t('home.health'), key: 'Health', icon: 'heart-outline', color: '#E91E63' },
  ];

  const renderProductCard = (product: any) => (
    <Card
      key={product.id}
      style={styles.productCard}
      variant="elevated"
      onPress={() => navigation.navigate('ProductDetails', { productId: product.id })}
    >
      <RTLView style={styles.productImage}>
        <RTLIcon name="image-outline" size={40} color="#CCCCCC" />
      </RTLView>
      <RTLText style={styles.productName} numberOfLines={2}>
        {product.name}
      </RTLText>
      <RTLView style={styles.productPricing}>
        <RTLText style={styles.productPrice}>
          {formatCurrency(product.price)}
        </RTLText>
        {product.originalPrice && product.originalPrice > product.price && (
          <RTLText style={styles.originalPrice}>
            {formatCurrency(product.originalPrice)}
          </RTLText>
        )}
      </RTLView>
      <RTLView style={styles.productRating}>
        <RTLIcon name="star" size={14} color="#FFD700" />
        <RTLText style={styles.ratingText}>{product.rating.toFixed(1)}</RTLText>
        <RTLText style={styles.reviewCount}>({product.reviewCount})</RTLText>
      </RTLView>
    </Card>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <RTLScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <LinearGradient colors={GRADIENTS.primary} style={styles.header}>
          <RTLView style={styles.headerContent}>
            <RTLView style={styles.headerLeft}>
              <RTLText style={styles.welcomeText}>{t('home.welcomeTo')}</RTLText>
              <RTLText style={styles.logoText}>{t('home.appName')}</RTLText>
            </RTLView>
            <RTLView style={styles.headerRight}>
              <LanguageSelector compact={true} style={styles.languageSelector} />
              <TouchableOpacity style={styles.headerButton} onPress={handleSearchPress}>
                <RTLIcon name="search-outline" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <TouchableOpacity onPress={handleLogout} style={styles.headerButton}>
                <RTLIcon name="log-out-outline" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </RTLView>
          </RTLView>
        </LinearGradient>

        {/* Categories */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('home.categories')}</RTLText>
          <RTLScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {categories.map((category, index) => (
              <TouchableOpacity
                key={index}
                style={styles.categoryCard}
                onPress={() => handleCategoryPress(category.key)}
              >
                <RTLView style={[styles.categoryIcon, { backgroundColor: category.color }]}>
                  <RTLIcon name={category.icon as any} size={24} color="#FFFFFF" />
                </RTLView>
                <RTLText style={styles.categoryName}>{category.name}</RTLText>
              </TouchableOpacity>
            ))}
          </RTLScrollView>
        </RTLView>

        {/* Featured Products */}
        <RTLView style={styles.section}>
          <RTLView style={styles.sectionHeader}>
            <RTLText style={styles.sectionTitle}>{t('home.featuredProducts')}</RTLText>
            <TouchableOpacity>
              <RTLText style={styles.seeAllText}>{t('home.viewAll')}</RTLText>
            </TouchableOpacity>
          </RTLView>

          {featuredProducts.length > 0 ? (
            <RTLScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsContainer}
            >
              {featuredProducts.map(renderProductCard)}
            </RTLScrollView>
          ) : (
            <EmptyState
              icon="cube-outline"
              title={t('home.noProductsFound')}
              description={t('home.checkBackLaterFeatured')}
              size="small"
            />
          )}
        </RTLView>

        {/* Best Selling */}
        <RTLView style={styles.section}>
          <RTLView style={styles.sectionHeader}>
            <RTLText style={styles.sectionTitle}>{t('home.bestSelling')}</RTLText>
            <TouchableOpacity>
              <RTLText style={styles.seeAllText}>{t('home.viewAll')}</RTLText>
            </TouchableOpacity>
          </RTLView>

          {bestSellingProducts.length > 0 ? (
            <RTLScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsContainer}
            >
              {bestSellingProducts.map(renderProductCard)}
            </RTLScrollView>
          ) : (
            <EmptyState
              icon="trending-up-outline"
              title={t('home.noProductsFound')}
              description={t('home.checkBackLaterTrending')}
              size="small"
            />
          )}
        </RTLView>

        {/* On Sale */}
        {saleProducts.length > 0 && (
          <RTLView style={styles.section}>
            <RTLView style={styles.sectionHeader}>
              <RTLText style={styles.sectionTitle}>{t('home.onSale')}</RTLText>
              <TouchableOpacity>
                <RTLText style={styles.seeAllText}>{t('home.viewAll')}</RTLText>
              </TouchableOpacity>
            </RTLView>

            <RTLScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsContainer}
            >
              {saleProducts.slice(0, 6).map(renderProductCard)}
            </RTLScrollView>
          </RTLView>
        )}

        {/* Recommendations */}
        <RecommendationsSection
          title={t('home.recommendedForYou')}
          limit={8}
          showReason={true}
        />

        {/* Quick Actions */}
        <RTLView style={styles.section}>
          <RTLText style={styles.sectionTitle}>{t('home.quickActions')}</RTLText>
          <RTLView style={styles.quickActions}>
            <Button
              title={t('home.browseAllShops')}
              onPress={() => navigation.navigate('Shops')}
              variant="outline"
              style={styles.quickActionButton}
              leftIcon={<RTLIcon name="storefront-outline" size={20} color="#667eea" />}
            />
            <Button
              title={t('home.viewCart')}
              onPress={() => navigation.navigate('Cart')}
              variant="outline"
              style={styles.quickActionButton}
              leftIcon={<RTLIcon name="bag-outline" size={20} color="#667eea" />}
            />
          </RTLView>
        </RTLView>
      </RTLScrollView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: SPACING.xl,
    },
    header: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.lg,
      marginBottom: SPACING.lg,
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerLeft: {
      flex: 1,
    },
    headerRight: {
      flexDirection: 'row',
      gap: SPACING.sm,
    },
    headerButton: {
      padding: SPACING.sm,
    },
    languageSelector: {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 20,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      marginRight: SPACING.xs,
    },
    welcomeText: {
      fontSize: FONT_SIZES.sm,
      color: '#FFFFFF',
      opacity: 0.8,
    },
    logoText: {
      fontSize: FONT_SIZES.xl,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
      marginTop: SPACING.xs,
    },
    section: {
      paddingHorizontal: SPACING.lg,
      marginBottom: SPACING.lg,
    },
    sectionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.md,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
    },
    seeAllText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    categoriesContainer: {
      paddingRight: SPACING.lg,
    },
    categoryCard: {
      alignItems: 'center',
      marginRight: SPACING.md,
      width: 80,
    },
    categoryIcon: {
      width: 60,
      height: 60,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    categoryName: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      textAlign: 'center',
      fontWeight: FONT_WEIGHTS.medium,
    },
    productsContainer: {
      paddingRight: SPACING.lg,
    },
    productCard: {
      width: 160,
      marginRight: SPACING.md,
    },
    productImage: {
      height: 120,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    productName: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
      minHeight: 32,
    },
    productPricing: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.xs,
    },
    productPrice: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginRight: SPACING.xs,
    },
    originalPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
    },
    productRating: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
      marginRight: SPACING.xs,
    },
    reviewCount: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
    },
    quickActions: {
      gap: SPACING.md,
    },
    quickActionButton: {
      justifyContent: 'flex-start',
      paddingHorizontal: SPACING.lg,
    },
  });
