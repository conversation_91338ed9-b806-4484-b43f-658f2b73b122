#!/usr/bin/env node

/**
 * SafeAreaView to RTLSafeAreaView Converter
 * 
 * This script automatically converts SafeAreaView usage to RTLSafeAreaView
 * in React Native TypeScript files to ensure proper RTL support.
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 SafeAreaView to RTLSafeAreaView Converter');
console.log('============================================\n');

// Configuration
const CONFIG = {
  srcDir: path.join(__dirname, '..', 'src'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.expo/,
    /build/,
    /dist/,
    /coverage/,
    /\.test\./,
    /\.spec\./,
    /RTLSafeAreaView\.tsx$/ // Don't modify the RTLSafeAreaView component itself
  ],
  fileExtensions: ['.tsx', '.ts']
};

// Files that should be excluded from conversion (e.g., modal components that need native SafeAreaView)
const EXCLUDED_FILES = [
  'LanguageSelector.tsx' // This file uses SafeAreaView in modals which is correct
];

class SafeAreaViewConverter {
  constructor() {
    this.results = {
      totalFiles: 0,
      modifiedFiles: 0,
      errors: [],
      changes: []
    };
  }

  /**
   * Main conversion process
   */
  async convert() {
    console.log('🔍 Scanning for SafeAreaView usage...\n');
    
    await this.scanDirectory(CONFIG.srcDir);
    
    this.printResults();
  }

  /**
   * Recursively scan directory for TypeScript files
   */
  async scanDirectory(dirPath) {
    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        // Skip excluded patterns
        if (this.shouldExclude(fullPath)) {
          continue;
        }
        
        if (entry.isDirectory()) {
          await this.scanDirectory(fullPath);
        } else if (entry.isFile() && this.isTargetFile(entry.name)) {
          await this.processFile(fullPath);
        }
      }
    } catch (error) {
      this.results.errors.push({
        file: dirPath,
        error: error.message
      });
    }
  }

  /**
   * Check if path should be excluded
   */
  shouldExclude(filePath) {
    const relativePath = path.relative(path.join(__dirname, '..'), filePath);
    
    // Check exclude patterns
    for (const pattern of CONFIG.excludePatterns) {
      if (pattern.test(relativePath)) {
        return true;
      }
    }
    
    // Check excluded files
    const fileName = path.basename(filePath);
    if (EXCLUDED_FILES.includes(fileName)) {
      return true;
    }
    
    return false;
  }

  /**
   * Check if file is a target TypeScript file
   */
  isTargetFile(fileName) {
    return CONFIG.fileExtensions.some(ext => fileName.endsWith(ext));
  }

  /**
   * Process individual file
   */
  async processFile(filePath) {
    try {
      this.results.totalFiles++;
      
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(path.join(__dirname, '..'), filePath);
      
      // Check if file contains SafeAreaView usage
      if (!this.containsSafeAreaView(content)) {
        return;
      }
      
      console.log(`📝 Processing: ${relativePath}`);
      
      // Convert SafeAreaView to RTLSafeAreaView
      const modifiedContent = this.convertSafeAreaView(content, relativePath);
      
      if (modifiedContent !== content) {
        // Write the modified content back to file
        fs.writeFileSync(filePath, modifiedContent, 'utf8');
        
        this.results.modifiedFiles++;
        this.results.changes.push({
          file: relativePath,
          changes: this.getChangeSummary(content, modifiedContent)
        });
        
        console.log(`   ✅ Converted SafeAreaView to RTLSafeAreaView`);
      }
      
    } catch (error) {
      this.results.errors.push({
        file: path.relative(path.join(__dirname, '..'), filePath),
        error: error.message
      });
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  /**
   * Check if content contains SafeAreaView usage
   */
  containsSafeAreaView(content) {
    return /<SafeAreaView|<\/SafeAreaView>/.test(content);
  }

  /**
   * Convert SafeAreaView to RTLSafeAreaView in content
   */
  convertSafeAreaView(content, filePath) {
    let modifiedContent = content;
    
    // Add RTLSafeAreaView import if SafeAreaView is being used
    if (/<SafeAreaView/.test(content)) {
      modifiedContent = this.addRTLSafeAreaViewImport(modifiedContent);
    }
    
    // Replace SafeAreaView tags with RTLSafeAreaView
    modifiedContent = modifiedContent.replace(/<SafeAreaView/g, '<RTLSafeAreaView');
    modifiedContent = modifiedContent.replace(/<\/SafeAreaView>/g, '</RTLSafeAreaView>');
    
    // Remove SafeAreaView from react-native imports if it exists
    modifiedContent = this.removeSafeAreaViewImport(modifiedContent);
    
    return modifiedContent;
  }

  /**
   * Add RTLSafeAreaView import to the file
   */
  addRTLSafeAreaViewImport(content) {
    // Check if RTLSafeAreaView is already imported
    if (/import.*RTLSafeAreaView.*from/.test(content)) {
      return content;
    }
    
    // Find existing RTL component imports
    const rtlImportMatch = content.match(/import\s*\{([^}]*)\}\s*from\s*['"][^'"]*RTL['"];?/);
    
    if (rtlImportMatch) {
      // Add RTLSafeAreaView to existing RTL imports
      const existingImports = rtlImportMatch[1].trim();
      const newImports = existingImports ? `${existingImports}, RTLSafeAreaView` : 'RTLSafeAreaView';
      return content.replace(rtlImportMatch[0], rtlImportMatch[0].replace(rtlImportMatch[1], ` ${newImports} `));
    } else {
      // Add new RTL import line
      const importMatch = content.match(/import.*from\s*['"]react-native['"];?\n/);
      if (importMatch) {
        const importLine = "import { RTLSafeAreaView } from '../components/RTL';\n";
        return content.replace(importMatch[0], importMatch[0] + importLine);
      }
    }
    
    return content;
  }

  /**
   * Remove SafeAreaView from react-native imports
   */
  removeSafeAreaViewImport(content) {
    // Find react-native import line
    const importMatch = content.match(/import\s*\{([^}]*)\}\s*from\s*['"]react-native['"];?/);
    
    if (importMatch) {
      const imports = importMatch[1]
        .split(',')
        .map(imp => imp.trim())
        .filter(imp => imp && imp !== 'SafeAreaView')
        .join(', ');
      
      if (imports) {
        return content.replace(importMatch[0], `import { ${imports} } from 'react-native';`);
      } else {
        // Remove the entire import line if no other imports remain
        return content.replace(importMatch[0] + '\n', '');
      }
    }
    
    return content;
  }

  /**
   * Get summary of changes made
   */
  getChangeSummary(originalContent, modifiedContent) {
    const originalMatches = (originalContent.match(/<SafeAreaView/g) || []).length;
    const modifiedMatches = (modifiedContent.match(/<RTLSafeAreaView/g) || []).length;
    
    return {
      safeAreaViewReplacements: originalMatches,
      rtlSafeAreaViewAdded: modifiedMatches
    };
  }

  /**
   * Print conversion results
   */
  printResults() {
    console.log('\n🎯 CONVERSION RESULTS');
    console.log('====================');
    console.log(`📊 Total files scanned: ${this.results.totalFiles}`);
    console.log(`✅ Files modified: ${this.results.modifiedFiles}`);
    console.log(`❌ Errors encountered: ${this.results.errors.length}\n`);
    
    if (this.results.changes.length > 0) {
      console.log('📝 Modified Files:');
      this.results.changes.forEach(change => {
        console.log(`  📄 ${change.file}`);
        console.log(`     - SafeAreaView → RTLSafeAreaView: ${change.changes.safeAreaViewReplacements} replacements`);
      });
      console.log();
    }
    
    if (this.results.errors.length > 0) {
      console.log('❌ Errors:');
      this.results.errors.forEach(error => {
        console.log(`  📄 ${error.file}: ${error.error}`);
      });
      console.log();
    }
    
    console.log('🎉 Conversion completed!');
    console.log('💡 Remember to test the app to ensure all changes work correctly.');
  }
}

// Run the converter
const converter = new SafeAreaViewConverter();
converter.convert().catch(console.error);
