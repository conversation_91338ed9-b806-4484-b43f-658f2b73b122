# Arabic Translation Inspector

A comprehensive script that inspects the entire VendorHub codebase to ensure complete Arabic translation coverage and RTL component usage.

## Features

🔍 **Comprehensive Analysis**
- Analyzes all screen and component files
- Checks translation key coverage
- Validates RTL component usage
- Identifies hardcoded strings
- Provides detailed compliance reports

🌍 **Translation Coverage**
- Compares English and Arabic translation keys
- Identifies missing Arabic translations
- Calculates translation coverage percentage
- Lists unused translation keys

🔄 **RTL Component Validation**
- Detects usage of basic React Native components
- Suggests RTL component replacements
- Validates RTL import statements
- Calculates RTL compliance percentage

📊 **Detailed Reporting**
- Overall summary with compliance percentages
- Screen-by-screen analysis by category
- Component analysis
- Issues and recommendations
- Final assessment with actionable insights

## Usage

### Command Line
```bash
# Run the inspector
node scripts/arabic-translation-inspector.js

# Or using npm script (if added to package.json)
npm run inspect:arabic
```

### Programmatic Usage
```javascript
const { ArabicTranslationInspector } = require('./scripts/arabic-translation-inspector');

const inspector = new ArabicTranslationInspector();
await inspector.inspect();
console.log(inspector.results);
```

## Output Example

```
🔍 Arabic Translation Inspector
=====================================

📋 Step 1: Analyzing I18n Service...
   ✅ Found 156 English translation keys
   ✅ Found 156 Arabic translation keys
   📊 Translation coverage: 100.0%

📱 Step 2: Inspecting Screen Files...
🧩 Step 3: Inspecting Component Files...
🔄 Step 4: Checking RTL Component Usage...
   ✅ RTL compliant files: 28
   ❌ Non-compliant files: 2
   📊 RTL coverage: 93.3%

📊 Step 5: Generating Report...

📊 COMPREHENSIVE ARABIC TRANSLATION REPORT
============================================

📋 OVERALL SUMMARY
==================
Total Files Analyzed: 30
Translation Compliant: 28 (93.3%)
RTL Compliant: 28 (93.3%)
Issues Found: 4

🌍 TRANSLATION COVERAGE
=======================
Total Translation Keys: 156
Arabic Translations: 156
Coverage: 100.0%

🔄 RTL COMPONENT USAGE
======================
RTL Coverage: 93.3%
Compliant Files: 28
Non-Compliant Files: 2

📱 SCREEN ANALYSIS
==================
admin: 4/4 (100.0%) compliant
auth: 3/3 (100.0%) compliant
chat: 2/2 (100.0%) compliant
public: 8/9 (88.9%) compliant
vendor: 6/6 (100.0%) compliant

🧩 COMPONENT ANALYSIS
=====================
Component Compliance: 5/6 (83.3%)

⚠️  ISSUES & RECOMMENDATIONS
=============================
RTL Issues (2):
  📄 src/screens/public/SomeScreen.tsx
     - Missing RTL component import
     - 3 basic components used

📋 Recommendations:
  3. Import RTL components from '../components/RTL'
  4. Replace basic React Native components with RTL equivalents

🎯 FINAL ASSESSMENT
===================
Overall Status: GOOD
Translation Coverage: 100.0%
RTL Coverage: 93.3%
Files Compliance: 93.3%

💡 Focus on addressing the issues above to improve Arabic/RTL support.

📝 Report generated on: 12/15/2024, 10:30:45 AM
```

## Exit Codes

- **0**: Success - No issues found, excellent Arabic/RTL support
- **1**: Issues found - Translation coverage < 95% or RTL coverage < 95% or other issues

## Configuration

The script can be configured by modifying the `CONFIG` object:

```javascript
const CONFIG = {
  srcDir: path.join(__dirname, '..', 'src'),
  i18nServicePath: path.join(__dirname, '..', 'src', 'services', 'I18nService.ts'),
  excludePatterns: [
    /node_modules/,
    /\.git/,
    /\.test\./,
    /\.spec\./
  ],
  screenDirectories: [
    'src/screens/admin',
    'src/screens/auth',
    'src/screens/chat',
    'src/screens/public',
    'src/screens/vendor'
  ],
  componentDirectories: [
    'src/components',
    'src/components/RTL'
  ]
};
```

## Integration with CI/CD

Add to your CI/CD pipeline to ensure Arabic/RTL compliance:

```yaml
# GitHub Actions example
- name: Check Arabic Translation Coverage
  run: node scripts/arabic-translation-inspector.js
```

```json
// package.json scripts
{
  "scripts": {
    "inspect:arabic": "node scripts/arabic-translation-inspector.js",
    "test:arabic": "npm run inspect:arabic",
    "pre-commit": "npm run inspect:arabic && npm test"
  }
}
```

## What It Checks

### Translation Compliance
- ✅ `useI18n` hook imported and used
- ✅ Translation function `t()` used for text
- ✅ No hardcoded translatable strings
- ✅ All translation keys have Arabic equivalents

### RTL Compliance
- ✅ RTL components imported from `../components/RTL`
- ✅ No basic React Native components used
- ✅ Proper RTL layout implementation
- ✅ Icon mirroring where appropriate

### File Coverage
- ✅ All screen files analyzed
- ✅ All component files analyzed
- ✅ Comprehensive reporting by category
- ✅ Actionable recommendations provided

## Troubleshooting

**Script fails to find I18nService.ts**
- Ensure the file exists at `src/services/I18nService.ts`
- Check file permissions

**High number of false positives for hardcoded strings**
- Review the `isTranslatableString()` function
- Add patterns to exclude non-translatable content

**RTL components not detected**
- Ensure RTL components are imported from the correct path
- Check component naming conventions

## Contributing

To extend the script:
1. Add new patterns to `TRANSLATION_PATTERNS`
2. Extend `RTL_COMPONENT_MAPPING` for new components
3. Add new validation rules in the analysis functions
4. Update reporting functions for new metrics
