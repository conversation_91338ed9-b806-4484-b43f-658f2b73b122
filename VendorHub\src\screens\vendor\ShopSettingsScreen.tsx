import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useAuth, useVendors } from '../../hooks';
import { Card, Button, Input, LoadingSpinner, ImagePicker, SingleImagePicker } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface ShopSettingsScreenProps {
  navigation: any;
}

interface ShopFormData {
  businessName: string;
  businessDescription: string;
  phone: string;
  address: string;
  website: string;
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
  };
  businessHours: string;
  logo: string | null;
  coverPhoto: string | null;
  businessPhotos: string[];
}

export const ShopSettingsScreen: React.FC<ShopSettingsScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { getVendorById, updateVendor } = useVendors();
  
  const [isLoading, setIsLoading] = useState(false);
  const [vendor, setVendor] = useState(null);
  const [formData, setFormData] = useState<ShopFormData>({
    businessName: '',
    businessDescription: '',
    phone: '',
    address: '',
    website: '',
    socialMedia: {
      facebook: '',
      instagram: '',
      twitter: '',
    },
    businessHours: '',
    logo: null,
    coverPhoto: null,
    businessPhotos: [],
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadVendorData();
  }, []);

  const loadVendorData = () => {
    if (user?.id) {
      const vendorData = getVendorById(user.id);
      if (vendorData) {
        setVendor(vendorData);
        setFormData({
          businessName: vendorData.businessName || '',
          businessDescription: vendorData.businessDescription || '',
          phone: vendorData.phone || '',
          address: vendorData.address || '',
          website: vendorData.website || '',
          socialMedia: {
            facebook: vendorData.socialMedia?.facebook || '',
            instagram: vendorData.socialMedia?.instagram || '',
            twitter: vendorData.socialMedia?.twitter || '',
          },
          businessHours: vendorData.businessHours || '',
          logo: vendorData.logo || null,
          coverPhoto: vendorData.coverPhoto || null,
          businessPhotos: vendorData.businessPhotos || [],
        });
      }
    }
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof ShopFormData],
          [child]: value,
        },
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.businessName.trim()) {
      newErrors.businessName = 'Business name is required';
    }

    if (!formData.businessDescription.trim()) {
      newErrors.businessDescription = 'Business description is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }

    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      newErrors.website = 'Please enter a valid website URL';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const updatedVendorData = {
        ...vendor,
        businessName: formData.businessName.trim(),
        businessDescription: formData.businessDescription.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim(),
        website: formData.website.trim(),
        socialMedia: formData.socialMedia,
        businessHours: formData.businessHours.trim(),
        logo: formData.logo,
        coverPhoto: formData.coverPhoto,
        businessPhotos: formData.businessPhotos,
        updatedAt: new Date().toISOString(),
      };

      await updateVendor(updatedVendorData);
      
      Alert.alert(
        'Success',
        'Your shop settings have been updated successfully!',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update shop settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!vendor) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <LoadingSpinner />
      </RTLSafeAreaView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            {/* Business Logo */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Business Logo</Text>

              <SingleImagePicker
                image={formData.logo}
                onImageChange={(image) => handleInputChange('logo', image)}
                title="Business Logo"
                subtitle="Upload your business logo"
                placeholder="Tap to add logo"
                aspectRatio={[1, 1]}
                size="large"
                shape="square"
              />

              <Text style={styles.helperText}>
                Your logo will appear on your shop page and product listings. Use a square image for best results.
              </Text>
            </Card>

            {/* Cover Photo */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Cover Photo</Text>

              <SingleImagePicker
                image={formData.coverPhoto}
                onImageChange={(image) => handleInputChange('coverPhoto', image)}
                title="Cover Photo"
                subtitle="Upload a cover photo for your shop"
                placeholder="Tap to add cover photo"
                aspectRatio={[16, 9]}
                size="large"
                shape="rectangle"
              />

              <Text style={styles.helperText}>
                This image will be displayed as the header of your shop page. Use a landscape image (16:9 ratio) for best results.
              </Text>
            </Card>

            {/* Business Photos */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Business Photos</Text>
              
              <ImagePicker
                images={formData.businessPhotos}
                onImagesChange={(images) => handleInputChange('businessPhotos', images)}
                maxImages={8}
                title="Business Photos"
                subtitle="Showcase your business with photos"
              />
              
              <Text style={styles.helperText}>
                Add photos of your workspace, team, or products to build trust with customers.
              </Text>
            </Card>

            {/* Basic Information */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Basic Information</Text>
              
              <Input
                label="Business Name *"
                placeholder="Enter your business name"
                value={formData.businessName}
                onChangeText={(value) => handleInputChange('businessName', value)}
                error={errors.businessName}
                style={styles.input}
              />

              <Input
                label="Business Description *"
                placeholder="Describe your business"
                value={formData.businessDescription}
                onChangeText={(value) => handleInputChange('businessDescription', value)}
                error={errors.businessDescription}
                multiline
                numberOfLines={4}
                style={styles.input}
              />

              <Input
                label="Phone Number *"
                placeholder="Enter your phone number"
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                error={errors.phone}
                keyboardType="phone-pad"
                style={styles.input}
              />

              <Input
                label="Business Address"
                placeholder="Enter your business address"
                value={formData.address}
                onChangeText={(value) => handleInputChange('address', value)}
                multiline
                numberOfLines={2}
                style={styles.input}
              />

              <Input
                label="Website"
                placeholder="https://yourwebsite.com"
                value={formData.website}
                onChangeText={(value) => handleInputChange('website', value)}
                error={errors.website}
                keyboardType="url"
                autoCapitalize="none"
                style={styles.input}
              />

              <Input
                label="Business Hours"
                placeholder="e.g., Mon-Fri 9AM-6PM"
                value={formData.businessHours}
                onChangeText={(value) => handleInputChange('businessHours', value)}
                style={styles.input}
              />
            </Card>

            {/* Social Media */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Social Media</Text>
              
              <Input
                label="Facebook"
                placeholder="Facebook page URL"
                value={formData.socialMedia.facebook}
                onChangeText={(value) => handleInputChange('socialMedia.facebook', value)}
                keyboardType="url"
                autoCapitalize="none"
                style={styles.input}
              />

              <Input
                label="Instagram"
                placeholder="Instagram profile URL"
                value={formData.socialMedia.instagram}
                onChangeText={(value) => handleInputChange('socialMedia.instagram', value)}
                keyboardType="url"
                autoCapitalize="none"
                style={styles.input}
              />

              <Input
                label="Twitter"
                placeholder="Twitter profile URL"
                value={formData.socialMedia.twitter}
                onChangeText={(value) => handleInputChange('socialMedia.twitter', value)}
                keyboardType="url"
                autoCapitalize="none"
                style={styles.input}
              />
            </Card>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                title="Cancel"
                onPress={() => navigation.goBack()}
                variant="outline"
                style={styles.cancelButton}
              />
              <Button
                title="Save Changes"
                onPress={handleSave}
                loading={isLoading}
                style={styles.saveButton}
                leftIcon={<Ionicons name="checkmark-outline" size={20} color="#FFFFFF" />}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: SPACING.xl,
  },
  content: {
    padding: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semibold,
    color: colors.text,
    marginBottom: SPACING.md,
  },
  input: {
    marginBottom: SPACING.md,
  },
  helperText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginTop: SPACING.sm,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.xl,
    gap: SPACING.md,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
});
