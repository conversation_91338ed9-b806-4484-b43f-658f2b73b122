import React from 'react';
import {
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  TextInput,
} from 'react-native';
import { useThemedStyles, useOrders, useVendors, useI18n } from '../../hooks';
import { Card, Button, EmptyState, StatusBadge } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Order } from '../../contexts/DataContext';

interface OrderManagementScreenProps {
  navigation: any;
}

export const OrderManagementScreen: React.FC<OrderManagementScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { getAllOrders } = useOrders();
  const { getVendorById } = useVendors();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedStatus, setSelectedStatus] = React.useState<string | null>(null);
  const [sortBy, setSortBy] = React.useState<'date' | 'amount' | 'status'>('date');

  const allOrders = getAllOrders();

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  // Filter and sort orders
  const filteredOrders = React.useMemo(() => {
    let filtered = allOrders.filter(order => {
      const matchesSearch = order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           order.customerName.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = !selectedStatus || order.status === selectedStatus;
      
      return matchesSearch && matchesStatus;
    });

    // Sort orders
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'amount':
          return b.totalAmount - a.totalAmount;
        case 'status':
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    return filtered;
  }, [allOrders, searchQuery, selectedStatus, sortBy]);

  const statuses = ['pending', 'processing', 'shipped', 'completed', 'cancelled'];

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'processing': return 'info';
      case 'shipped': return 'primary';
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const renderOrderItem = ({ item }: { item: Order }) => {
    const vendor = getVendorById(item.vendorId);
    
    return (
      <Card style={styles.orderCard} variant="elevated">
        <TouchableOpacity
          style={styles.orderContent}
          onPress={() => navigation.navigate('OrderDetails', { orderId: item.id })}
        >
          <RTLView style={styles.orderHeader}>
            <RTLView style={styles.orderInfo}>
              <RTLText style={styles.orderId}>Order #{item.id}</RTLText>
              <RTLText style={styles.orderDate}>
                {new Date(item.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </RTLText>
            </RTLView>
            <StatusBadge
              status={item.status}
              variant={getStatusVariant(item.status)}
            />
          </RTLView>

          <RTLView style={styles.orderDetails}>
            <RTLView style={styles.customerInfo}>
              <RTLIcon name="person-outline" size={16} color="#666" />
              <RTLText style={styles.customerName}>{item.customerName}</RTLText>
            </RTLView>

            <RTLView style={styles.vendorInfo}>
              <RTLIcon name="storefront-outline" size={16} color="#666" />
              <RTLText style={styles.vendorName}>
                {vendor?.businessName || 'Unknown Vendor'}
              </RTLText>
            </RTLView>
          </RTLView>

          <RTLView style={styles.orderItems}>
            <RTLText style={styles.itemsLabel}>
              {item.items.length} item{item.items.length !== 1 ? 's' : ''}
            </RTLText>
            <RTLView style={styles.itemsList}>
              {item.items.slice(0, 2).map((orderItem, index) => (
                <RTLText key={index} style={styles.itemText} numberOfLines={1}>
                  {orderItem.quantity}x {orderItem.productName}
                </RTLText>
              ))}
              {item.items.length > 2 && (
                <RTLText style={styles.moreItemsText}>
                  +{item.items.length - 2} more
                </RTLText>
              )}
            </RTLView>
          </RTLView>

          <RTLView style={styles.orderFooter}>
            <RTLView style={styles.totalContainer}>
              <RTLText style={styles.totalLabel}>Total:</RTLText>
              <RTLText style={styles.totalAmount}>
                {formatCurrency(item.totalAmount)}
              </RTLText>
            </RTLView>

            <TouchableOpacity style={styles.actionButton}>
              <RTLIcon name="chevron-forward" size={20} color="#666" />
            </TouchableOpacity>
          </RTLView>
        </TouchableOpacity>
      </Card>
    );
  };

  const renderHeader = () => (
    <RTLView style={styles.header}>
      {/* Search Bar */}
      <RTLView style={styles.searchContainer}>
        <RTLIcon name="search-outline" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search orders or customers..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <RTLIcon name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        )}
      </RTLView>

      {/* Filters and Sort */}
      <View style={styles.controlsContainer}>
        {/* Status Filter */}
        <View style={styles.filterSection}>
          <Text style={styles.filterLabel}>Status:</Text>
          <View style={styles.filterChips}>
            <TouchableOpacity
              style={[styles.filterChip, !selectedStatus && styles.filterChipActive]}
              onPress={() => setSelectedStatus(null)}
            >
              <Text style={[styles.filterChipText, !selectedStatus && styles.filterChipTextActive]}>
                All
              </Text>
            </TouchableOpacity>
            
            {statuses.map(status => (
              <TouchableOpacity
                key={status}
                style={[styles.filterChip, selectedStatus === status && styles.filterChipActive]}
                onPress={() => setSelectedStatus(selectedStatus === status ? null : status)}
              >
                <Text style={[styles.filterChipText, selectedStatus === status && styles.filterChipTextActive]}>
                  {status}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Sort Options */}
        <View style={styles.sortSection}>
          <Text style={styles.filterLabel}>Sort by:</Text>
          <View style={styles.filterChips}>
            {[
              { key: 'date', label: 'Date' },
              { key: 'amount', label: 'Amount' },
              { key: 'status', label: 'Status' },
            ].map(option => (
              <TouchableOpacity
                key={option.key}
                style={[styles.filterChip, sortBy === option.key && styles.filterChipActive]}
                onPress={() => setSortBy(option.key as any)}
              >
                <Text style={[styles.filterChipText, sortBy === option.key && styles.filterChipTextActive]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Results Count */}
      <View style={styles.resultsContainer}>
        <Text style={styles.resultsText}>
          {filteredOrders.length} order{filteredOrders.length !== 1 ? 's' : ''} found
        </Text>
        
        {(searchQuery || selectedStatus) && (
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={() => {
              setSearchQuery('');
              setSelectedStatus(null);
            }}
          >
            <Text style={styles.clearFiltersText}>Clear Filters</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <FlatList
        data={filteredOrders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="receipt-outline"
            title={t('orders.noOrdersFound')}
            description={
              searchQuery || selectedStatus
                ? "Try adjusting your search or filters"
                : "No orders have been placed yet"
            }
          />
        }
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: SPACING.lg,
      paddingBottom: SPACING.xl,
    },
    header: {
      marginBottom: SPACING.lg,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      marginBottom: SPACING.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    searchIcon: {
      marginRight: SPACING.sm,
    },
    searchInput: {
      flex: 1,
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    controlsContainer: {
      marginBottom: SPACING.md,
    },
    filterSection: {
      marginBottom: SPACING.md,
    },
    sortSection: {
      marginBottom: SPACING.md,
    },
    filterLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    filterChips: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.sm,
    },
    filterChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterChipActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    filterChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textTransform: 'capitalize',
    },
    filterChipTextActive: {
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.semibold,
    },
    resultsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    resultsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    clearFiltersButton: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
    },
    clearFiltersText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    orderCard: {
      marginBottom: SPACING.md,
    },
    orderContent: {
      padding: SPACING.md,
    },
    orderHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: SPACING.md,
    },
    orderInfo: {
      flex: 1,
    },
    orderId: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    orderDate: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    orderDetails: {
      marginBottom: SPACING.md,
    },
    customerInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: SPACING.sm,
    },
    customerName: {
      fontSize: FONT_SIZES.sm,
      color: colors.textPrimary,
      marginLeft: SPACING.sm,
      fontWeight: FONT_WEIGHTS.medium,
    },
    vendorInfo: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    vendorName: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.sm,
    },
    orderItems: {
      marginBottom: SPACING.md,
    },
    itemsLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    itemsList: {
      gap: SPACING.xs,
    },
    itemText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    moreItemsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontStyle: 'italic',
    },
    orderFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    totalContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    totalLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginRight: SPACING.sm,
    },
    totalAmount: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    actionButton: {
      padding: SPACING.sm,
    },
  });
