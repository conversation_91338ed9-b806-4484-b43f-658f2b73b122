import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useThemedStyles, useProducts, useI18n } from '../../hooks';
import { Card, Button, Input, LoadingSpinner, ImagePicker } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView, RTLScrollView } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { PRODUCT_CATEGORIES } from '../../constants';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { ProductCategory, Product } from '../../contexts/DataContext';

interface EditProductScreenProps {
  navigation: any;
  route: {
    params: {
      productId: string;
    };
  };
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  originalPrice: string;
  category: ProductCategory | '';
  inventory: string;
  tags: string;
  isActive: boolean;
  images: string[];
}

export const EditProductScreen: React.FC<EditProductScreenProps> = ({ navigation, route }) => {
  const { productId } = route.params;
  const styles = useThemedStyles(createStyles);
  const { getProductById, updateProduct } = useProducts();
  const { t } = useI18n();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    originalPrice: '',
    category: '',
    inventory: '',
    tags: '',
    isActive: true,
    images: [],
  });

  const [errors, setErrors] = useState<Partial<ProductFormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);

  useEffect(() => {
    const productData = getProductById(productId);
    if (productData) {
      setProduct(productData);
      setFormData({
        name: productData.name,
        description: productData.description,
        price: productData.price.toString(),
        originalPrice: productData.originalPrice?.toString() || '',
        category: productData.category,
        inventory: productData.inventory.toString(),
        tags: productData.tags.join(', '),
        isActive: productData.isActive,
        images: productData.images || [],
      });
    } else {
      Alert.alert(t('common.error'), t('errors.productNotFound'), [
        { text: t('common.done'), onPress: () => navigation.goBack() }
      ]);
    }
  }, [productId, getProductById, navigation]);

  const validateForm = (): boolean => {
    const newErrors: Partial<ProductFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('products.productNameRequired');
    }

    if (!formData.description.trim()) {
      newErrors.description = t('products.productDescriptionRequired');
    }

    if (!formData.price.trim()) {
      newErrors.price = t('products.priceRequired');
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = t('products.validPriceRequired');
    }

    if (formData.originalPrice && (isNaN(Number(formData.originalPrice)) || Number(formData.originalPrice) <= 0)) {
      newErrors.originalPrice = t('products.validOriginalPriceRequired');
    }

    if (!formData.category) {
      newErrors.category = t('products.categoryRequired');
    }

    if (!formData.inventory.trim()) {
      newErrors.inventory = t('products.inventoryRequired');
    } else if (isNaN(Number(formData.inventory)) || Number(formData.inventory) < 0) {
      newErrors.inventory = t('products.validInventoryRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !product) return;

    setIsLoading(true);
    try {
      const updatedData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: Number(formData.price),
        originalPrice: formData.originalPrice ? Number(formData.originalPrice) : undefined,
        category: formData.category as ProductCategory,
        inventory: Number(formData.inventory),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        isActive: formData.isActive,
        images: formData.images,
        updatedAt: new Date().toISOString(),
      };

      await updateProduct(productId, updatedData);
      
      Alert.alert(
        t('common.success'),
        t('products.productUpdatedSuccessfully'),
        [
          {
            text: t('common.done'),
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert(t('common.error'), t('products.failedToUpdateProduct'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleCategorySelect = (category: ProductCategory) => {
    setFormData(prev => ({ ...prev, category }));
    setShowCategoryPicker(false);
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: undefined }));
    }
  };

  const handleToggleStatus = () => {
    const newStatus = !formData.isActive;
    setFormData(prev => ({ ...prev, isActive: newStatus }));
    
    Alert.alert(
      newStatus ? t('products.activateProduct') : t('products.deactivateProduct'),
      newStatus ? t('products.activateProductConfirmation') : t('products.deactivateProductConfirmation'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: newStatus ? t('products.activate') : t('products.deactivate'),
          onPress: () => {
            // Status is already updated in formData
          },
        },
      ]
    );
  };

  const renderCategoryPicker = () => (
    <Card style={styles.categoryPicker} variant="elevated">
      <RTLText style={styles.categoryPickerTitle}>{t('products.selectCategory')}</RTLText>
      {Object.values(PRODUCT_CATEGORIES).map((category) => (
        <TouchableOpacity
          key={category}
          style={styles.categoryOption}
          onPress={() => handleCategorySelect(category)}
        >
          <RTLText style={styles.categoryOptionText}>{category}</RTLText>
          <RTLIcon name="chevron-forward" size={20} color="#CCCCCC" />
        </TouchableOpacity>
      ))}
      <Button
        title={t('common.cancel')}
        onPress={() => setShowCategoryPicker(false)}
        variant="outline"
        style={styles.cancelButton}
      />
    </Card>
  );

  if (!product) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <RTLView style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <RTLText style={styles.loadingText}>Loading product...</RTLText>
        </RTLView>
      </RTLSafeAreaView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <RTLScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <RTLView style={styles.content}>
            {/* Header */}
            <RTLView style={styles.header}>
              <RTLText style={styles.title}>{t('products.editProduct')}</RTLText>
              <RTLText style={styles.subtitle}>
                Update your product information below
              </RTLText>
            </RTLView>

            {/* Product Status */}
            <Card style={styles.section} variant="outlined">
              <RTLView style={styles.statusSection}>
                <RTLView style={styles.statusInfo}>
                  <RTLText style={styles.sectionTitle}>{t('products.productStatus')}</RTLText>
                  <RTLText style={styles.statusDescription}>
                    {formData.isActive ? t('products.productActiveDescription') : t('products.productInactiveDescription')}
                  </RTLText>
                </RTLView>
                <TouchableOpacity
                  style={[styles.statusToggle, formData.isActive && styles.statusToggleActive]}
                  onPress={handleToggleStatus}
                >
                  <RTLView style={[styles.statusToggleThumb, formData.isActive && styles.statusToggleThumbActive]} />
                </TouchableOpacity>
              </RTLView>
            </Card>

            {/* Basic Information */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.basicInformation')}</RTLText>

              <Input
                label={`${t('products.productName')} *`}
                placeholder={t('products.productNamePlaceholder')}
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                error={errors.name}
                style={styles.input}
              />

              <Input
                label={`${t('products.productDescription')} *`}
                placeholder={t('products.productDescriptionPlaceholder')}
                value={formData.description}
                onChangeText={(value) => handleInputChange('description', value)}
                error={errors.description}
                multiline
                numberOfLines={4}
                style={styles.input}
              />

              <TouchableOpacity
                style={styles.categorySelector}
                onPress={() => setShowCategoryPicker(true)}
              >
                <RTLText style={styles.categoryLabel}>{`${t('products.productCategory')} *`}</RTLText>
                <RTLView style={styles.categoryValue}>
                  <RTLText style={[styles.categoryText, !formData.category && styles.placeholderText]}>
                    {formData.category || t('products.selectCategory')}
                  </RTLText>
                  <RTLIcon name="chevron-down" size={20} color="#CCCCCC" />
                </RTLView>
                {errors.category && (
                  <RTLText style={styles.errorText}>{errors.category}</RTLText>
                )}
              </TouchableOpacity>
            </Card>

            {/* Pricing */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.pricingInventory')}</RTLText>

              <Input
                label={`${t('products.productPrice')} *`}
                placeholder={t('products.productPricePlaceholder')}
                value={formData.price}
                onChangeText={(value) => handleInputChange('price', value)}
                error={errors.price}
                keyboardType="numeric"
                leftIcon={<RTLText style={styles.currencySymbol}>BD</RTLText>}
                style={styles.input}
              />

              <Input
                label={t('products.productOriginalPrice')}
                placeholder={t('products.productOriginalPricePlaceholder')}
                value={formData.originalPrice}
                onChangeText={(value) => handleInputChange('originalPrice', value)}
                error={errors.originalPrice}
                keyboardType="numeric"
                leftIcon={<RTLText style={styles.currencySymbol}>BD</RTLText>}
                style={styles.input}
              />
            </Card>

            {/* Inventory */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.productInventory')}</RTLText>

              <Input
                label={`${t('products.productInventory')} *`}
                placeholder={t('products.productInventoryPlaceholder')}
                value={formData.inventory}
                onChangeText={(value) => handleInputChange('inventory', value)}
                error={errors.inventory}
                keyboardType="numeric"
                style={styles.input}
              />
            </Card>

            {/* Product Images */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.productImages')}</RTLText>

              <ImagePicker
                images={formData.images}
                onImagesChange={(images) => handleInputChange('images', images)}
                maxImages={5}
                title={t('products.productImages')}
                subtitle={t('products.updateProductImages')}
              />

              <RTLText style={styles.helperText}>
                High-quality images help increase sales. The first image will be used as the main product image.
              </RTLText>
            </Card>

            {/* Additional Details */}
            <Card style={styles.section} variant="outlined">
              <RTLText style={styles.sectionTitle}>{t('products.additionalDetails')}</RTLText>

              <Input
                label={t('products.productTags')}
                placeholder={t('products.productTagsPlaceholder')}
                value={formData.tags}
                onChangeText={(value) => handleInputChange('tags', value)}
                style={styles.input}
              />
            </Card>

            {/* Action Buttons */}
            <RTLView style={styles.actionButtons}>
              <Button
                title={t('common.cancel')}
                onPress={() => navigation.goBack()}
                variant="outline"
                style={styles.cancelActionButton}
              />
              <Button
                title={t('products.updateProduct')}
                onPress={handleSubmit}
                loading={isLoading}
                style={styles.submitButton}
                leftIcon={<RTLIcon name="checkmark-outline" size={20} color="#FFFFFF" />}
              />
            </RTLView>
          </RTLView>
        </RTLScrollView>
      </KeyboardAvoidingView>

      {/* Category Picker Modal */}
      {showCategoryPicker && (
        <RTLView style={styles.modalOverlay}>
          {renderCategoryPicker()}
        </RTLView>
      )}
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      marginTop: SPACING.md,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: SPACING.lg,
    },
    header: {
      marginBottom: SPACING.xl,
    },
    title: {
      fontSize: FONT_SIZES.xxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    subtitle: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      lineHeight: 22,
    },
    section: {
      marginBottom: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    statusSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    statusInfo: {
      flex: 1,
      marginRight: SPACING.md,
    },
    statusDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 18,
    },
    statusToggle: {
      width: 50,
      height: 30,
      borderRadius: 15,
      backgroundColor: colors.border,
      justifyContent: 'center',
      padding: 2,
    },
    statusToggleActive: {
      backgroundColor: '#4CAF50',
    },
    statusToggleThumb: {
      width: 26,
      height: 26,
      borderRadius: 13,
      backgroundColor: '#FFFFFF',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    statusToggleThumbActive: {
      transform: [{ translateX: 20 }],
    },
    input: {
      marginBottom: SPACING.md,
    },
    categorySelector: {
      marginBottom: SPACING.md,
    },
    categoryLabel: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    categoryValue: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: SPACING.md,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
    },
    categoryText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    placeholderText: {
      color: colors.textSecondary,
    },
    errorText: {
      fontSize: FONT_SIZES.sm,
      color: '#FF6B6B',
      marginTop: SPACING.xs,
    },
    currencySymbol: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    actionButtons: {
      flexDirection: 'row',
      gap: SPACING.md,
      marginTop: SPACING.xl,
      marginBottom: SPACING.xl,
    },
    cancelActionButton: {
      flex: 1,
    },
    submitButton: {
      flex: 2,
    },
    modalOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: SPACING.xl,
    },
    categoryPicker: {
      width: '100%',
      maxHeight: '80%',
    },
    categoryPickerTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
      textAlign: 'center',
    },
    categoryOption: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    categoryOptionText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    cancelButton: {
      marginTop: SPACING.md,
    },
  });
