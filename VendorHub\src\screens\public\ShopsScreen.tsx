import React from 'react';
import { StyleSheet, FlatList, TouchableOpacity, RefreshControl, TextInput } from 'react-native';
import { useThemedStyles, useVendors, useProducts, useI18n } from '../../hooks';
import { Card, Button, EmptyState, StatusBadge } from '../../components';
import { RTLView, RTLText, RTLIcon, RTLSafeAreaView } from '../../components/RTL';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Vendor } from '../../contexts/DataContext';

interface ShopsScreenProps {
  navigation: any;
}

export const ShopsScreen: React.FC<ShopsScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { getApprovedVendors } = useVendors();
  const { getProductsByVendor } = useProducts();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [selectedCategory, setSelectedCategory] = React.useState<string | null>(null);

  const approvedVendors = getApprovedVendors();

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  // Filter vendors based on search and category
  const filteredVendors = React.useMemo(() => {
    return approvedVendors.filter(vendor => {
      const matchesSearch = vendor.businessName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           vendor.businessDescription.toLowerCase().includes(searchQuery.toLowerCase());
      
      if (!selectedCategory) return matchesSearch;
      
      // Check if vendor has products in the selected category
      const vendorProducts = getProductsByVendor(vendor.id);
      const hasCategory = vendorProducts.some(product => product.category === selectedCategory);
      
      return matchesSearch && hasCategory;
    });
  }, [approvedVendors, searchQuery, selectedCategory, getProductsByVendor]);

  // Get all categories from all products
  const categories = React.useMemo(() => {
    const allCategories = new Set<string>();
    approvedVendors.forEach(vendor => {
      const vendorProducts = getProductsByVendor(vendor.id);
      vendorProducts.forEach(product => {
        allCategories.add(product.category);
      });
    });
    return Array.from(allCategories).sort();
  }, [approvedVendors, getProductsByVendor]);

  const renderVendorCard = ({ item }: { item: Vendor }) => {
    const vendorProducts = getProductsByVendor(item.id);
    const productCount = vendorProducts.length;
    const avgRating = vendorProducts.length > 0 
      ? vendorProducts.reduce((sum, p) => sum + p.rating, 0) / vendorProducts.length 
      : 0;

    return (
      <Card style={styles.vendorCard} variant="elevated">
        <TouchableOpacity
          style={styles.vendorContent}
          onPress={() => navigation.navigate('VendorShop', { vendorId: item.id })}
        >
          {/* Vendor Header */}
          <RTLView style={styles.vendorHeader}>
            <RTLView style={styles.vendorLogo}>
              <RTLIcon name="storefront" size={32} color="#667eea" />
            </RTLView>
            <RTLView style={styles.vendorInfo}>
              <RTLText style={styles.vendorName} numberOfLines={1}>
                {item.businessName}
              </RTLText>
              <RTLText style={styles.vendorCategory}>
                {vendorProducts.length > 0 ? vendorProducts[0].category : t('shops.general')}
              </RTLText>
              <RTLView style={styles.ratingContainer}>
                <RTLIcon name="star" size={14} color="#FFD700" />
                <RTLText style={styles.ratingText}>
                  {avgRating > 0 ? avgRating.toFixed(1) : t('shops.newShop')}
                </RTLText>
                <RTLText style={styles.productCount}>
                  • {productCount} {productCount === 1 ? t('products.product') : t('shops.productsCount')}
                </RTLText>
              </RTLView>
            </RTLView>
            <TouchableOpacity style={styles.favoriteButton}>
              <RTLIcon name="heart-outline" size={20} color="#666" />
            </TouchableOpacity>
          </RTLView>

          {/* Vendor Description */}
          <RTLText style={styles.vendorDescription} numberOfLines={2}>
            {item.businessDescription}
          </RTLText>

          {/* Featured Products Preview */}
          {vendorProducts.length > 0 && (
            <RTLView style={styles.productsPreview}>
              <RTLText style={styles.previewTitle}>{t('home.featuredProducts')}</RTLText>
              <RTLView style={styles.productsList}>
                {vendorProducts.slice(0, 3).map((product, index) => (
                  <RTLView key={product.id} style={styles.productPreview}>
                    <RTLView style={styles.productImage}>
                      <RTLIcon name="image-outline" size={16} color="#CCCCCC" />
                    </RTLView>
                    <RTLView style={styles.productInfo}>
                      <RTLText style={styles.productName} numberOfLines={1}>
                        {product.name}
                      </RTLText>
                      <RTLText style={styles.productPrice}>
                        {formatCurrency(product.price)}
                      </RTLText>
                    </RTLView>
                  </RTLView>
                ))}
                {vendorProducts.length > 3 && (
                  <TouchableOpacity style={styles.moreProducts}>
                    <RTLText style={styles.moreProductsText}>
                      +{vendorProducts.length - 3} more
                    </RTLText>
                  </TouchableOpacity>
                )}
              </RTLView>
            </RTLView>
          )}

          {/* Vendor Footer */}
          <RTLView style={styles.vendorFooter}>
            <RTLView style={styles.vendorLocation}>
              <RTLIcon name="location-outline" size={14} color="#666" />
              <RTLText style={styles.locationText}>
                {item.address.city}, {item.address.state}
              </RTLText>
            </RTLView>
            <TouchableOpacity style={styles.visitButton}>
              <RTLText style={styles.visitButtonText}>{t('shops.viewShop')}</RTLText>
              <RTLIcon name="chevron-forward" size={16} color="#667eea" />
            </TouchableOpacity>
          </RTLView>
        </TouchableOpacity>
      </Card>
    );
  };

  const renderHeader = () => (
    <RTLView style={styles.header}>
      {/* Search Bar */}
      <RTLView style={styles.searchContainer}>
        <RTLIcon name="search-outline" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder={t('shops.searchShops')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <RTLIcon name="close-circle" size={20} color="#999" />
          </TouchableOpacity>
        )}
      </RTLView>

      {/* Category Filter */}
      <RTLView style={styles.categoriesSection}>
        <RTLText style={styles.categoriesLabel}>{t('shops.categories')}</RTLText>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={[{ name: t('shops.all'), key: null }, ...categories.map(cat => ({ name: cat, key: cat }))]}
          keyExtractor={(item) => item.key || 'all'}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.categoryChip,
                selectedCategory === item.key && styles.categoryChipActive
              ]}
              onPress={() => setSelectedCategory(item.key)}
            >
              <RTLText style={[
                styles.categoryChipText,
                selectedCategory === item.key && styles.categoryChipTextActive
              ]}>
                {item.name}
              </RTLText>
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.categoriesList}
        />
      </RTLView>

      {/* Results Count */}
      <RTLView style={styles.resultsContainer}>
        <RTLText style={styles.resultsText}>
          {filteredVendors.length} {filteredVendors.length === 1 ? t('shops.shop') : t('nav.shops')} {t('shops.found')}
        </RTLText>

        {(searchQuery || selectedCategory) && (
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={() => {
              setSearchQuery('');
              setSelectedCategory(null);
            }}
          >
            <RTLText style={styles.clearFiltersText}>{t('cart.clearAll')}</RTLText>
          </TouchableOpacity>
        )}
      </RTLView>
    </RTLView>
  );

  return (
    <RTLSafeAreaView style={styles.container}>
      <FlatList
        data={filteredVendors}
        renderItem={renderVendorCard}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="storefront-outline"
            title={t('shops.noShopsFound')}
            description={
              searchQuery || selectedCategory
                ? t('shops.adjustSearchFilter')
                : t('shops.noApprovedShops')
            }
          />
        }
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: SPACING.lg,
      paddingBottom: SPACING.xl,
    },
    header: {
      marginBottom: SPACING.lg,
    },
    searchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.md,
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      marginBottom: SPACING.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    searchIcon: {
      marginRight: SPACING.sm,
    },
    searchInput: {
      flex: 1,
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    categoriesSection: {
      marginBottom: SPACING.md,
    },
    categoriesLabel: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    categoriesList: {
      paddingRight: SPACING.lg,
    },
    categoryChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
      marginRight: SPACING.sm,
    },
    categoryChipActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    categoryChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textTransform: 'capitalize',
    },
    categoryChipTextActive: {
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.semibold,
    },
    resultsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    resultsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    clearFiltersButton: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
    },
    clearFiltersText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    vendorCard: {
      marginBottom: SPACING.lg,
    },
    vendorContent: {
      padding: SPACING.md,
    },
    vendorHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: SPACING.md,
    },
    vendorLogo: {
      width: 60,
      height: 60,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BORDER_RADIUS.md,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    vendorInfo: {
      flex: 1,
    },
    vendorName: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    vendorCategory: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
      marginBottom: SPACING.xs,
      textTransform: 'capitalize',
    },
    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    productCount: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    favoriteButton: {
      padding: SPACING.sm,
    },
    vendorDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 20,
      marginBottom: SPACING.md,
    },
    productsPreview: {
      marginBottom: SPACING.md,
    },
    previewTitle: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    productsList: {
      gap: SPACING.sm,
    },
    productPreview: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productImage: {
      width: 32,
      height: 32,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BORDER_RADIUS.sm,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
    },
    productInfo: {
      flex: 1,
    },
    productName: {
      fontSize: FONT_SIZES.sm,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    productPrice: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.primary,
    },
    moreProducts: {
      paddingVertical: SPACING.sm,
      alignItems: 'center',
    },
    moreProductsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    vendorFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    vendorLocation: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    locationText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    visitButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
    },
    visitButtonText: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
      marginRight: SPACING.xs,
    },
  });
