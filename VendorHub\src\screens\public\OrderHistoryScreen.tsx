import React, { useState, useMemo } from 'react';
import { StyleSheet, TouchableOpacity, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useAuth, useOrders, useI18n } from '../../hooks';
import { Card, Button, Input, EmptyState, StatusBadge, RTLView, RTLText, RTLSafeAreaView, RTLSectionList, RTLIcon } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  ICON_SIZES,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Order } from '../../contexts/DataContext';

interface OrderHistoryScreenProps {
  navigation: any;
}

interface OrderSection {
  title: string;
  data: Order[];
}

export const OrderHistoryScreen: React.FC<OrderHistoryScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { getCustomerOrders } = useOrders();
  const { t } = useI18n();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled'>('all');

  const customerOrders = user ? getCustomerOrders(user.id) : [];

  const filteredOrders = useMemo(() => {
    let filtered = customerOrders;

    // Filter by status
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(order => order.status === selectedFilter);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(order => 
        order.id.toLowerCase().includes(query) ||
        order.items.some(item => item.productName.toLowerCase().includes(query))
      );
    }

    return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [customerOrders, selectedFilter, searchQuery]);

  const groupedOrders = useMemo(() => {
    const groups: { [key: string]: Order[] } = {};
    
    filteredOrders.forEach(order => {
      const date = new Date(order.createdAt);
      const monthYear = date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
      
      if (!groups[monthYear]) {
        groups[monthYear] = [];
      }
      groups[monthYear].push(order);
    });

    return Object.entries(groups).map(([title, data]) => ({
      title,
      data,
    }));
  }, [filteredOrders]);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleOrderPress = (orderId: string) => {
    navigation.navigate('OrderDetails', { orderId });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#FF9800';
      case 'confirmed': return '#2196F3';
      case 'shipped': return '#9C27B0';
      case 'delivered': return '#4CAF50';
      case 'cancelled': return '#F44336';
      default: return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'confirmed': return 'checkmark-circle-outline';
      case 'shipped': return 'car-outline';
      case 'delivered': return 'checkmark-done-outline';
      case 'cancelled': return 'close-circle-outline';
      default: return 'help-circle-outline';
    }
  };

  const filterOptions = [
    { key: 'all', label: t('orders.allOrders') },
    { key: 'pending', label: t('orders.pending') },
    { key: 'confirmed', label: t('orders.confirmed') },
    { key: 'shipped', label: t('orders.shipped') },
    { key: 'delivered', label: t('orders.delivered') },
    { key: 'cancelled', label: t('orders.cancelled') },
  ];

  const renderOrderItem = ({ item }: { item: Order }) => (
    <Card style={styles.orderCard} variant="elevated">
      <TouchableOpacity
        style={styles.orderContent}
        onPress={() => handleOrderPress(item.id)}
      >
        <View style={styles.orderHeader}>
          <View style={styles.orderInfo}>
            <Text style={styles.orderId}>Order #{item.id.slice(-6)}</Text>
            <Text style={styles.orderDate}>
              {new Date(item.createdAt).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
              })}
            </Text>
          </View>
          <StatusBadge 
            status={item.status} 
            color={getStatusColor(item.status)}
            icon={getStatusIcon(item.status)}
          />
        </View>

        <View style={styles.orderItems}>
          <Text style={styles.itemsText}>
            {item.items.length} item{item.items.length !== 1 ? 's' : ''}
          </Text>
          <Text style={styles.itemsPreview} numberOfLines={1}>
            {item.items.map(item => item.productName).join(', ')}
          </Text>
        </View>

        <View style={styles.orderFooter}>
          <Text style={styles.orderTotal}>
            {formatCurrency(item.totalAmount)}
          </Text>
          <View style={styles.orderActions}>
            <Ionicons name="chevron-forward" size={20} color="#CCCCCC" />
          </View>
        </View>
      </TouchableOpacity>
    </Card>
  );

  const renderSectionHeader = ({ section }: { section: OrderSection }) => (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{section.title}</Text>
      <Text style={styles.sectionCount}>
        {section.data.length} order{section.data.length !== 1 ? 's' : ''}
      </Text>
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <Input
        placeholder="Search orders..."
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon={<Ionicons name="search-outline" size={20} color="#666" />}
        style={styles.searchInput}
      />

      {/* Filter Chips */}
      <FlatList
        data={filterOptions}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        contentContainerStyle={styles.filterContainer}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterChip,
              selectedFilter === item.key && styles.filterChipActive,
            ]}
            onPress={() => setSelectedFilter(item.key as any)}
          >
            <Text
              style={[
                styles.filterChipText,
                selectedFilter === item.key && styles.filterChipTextActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
      />

      {/* Results Info */}
      <View style={styles.resultsInfo}>
        <Text style={styles.resultsText}>
          {filteredOrders.length} order{filteredOrders.length !== 1 ? 's' : ''} found
        </Text>
      </View>
    </View>
  );

  if (!user) {
    return (
      <RTLSafeAreaView style={styles.container}>
        <EmptyState
          icon="person-outline"
          title="Not Logged In"
          description="Please log in to view your order history"
          actionButton={
            <Button
              title={t('auth.login')}
              onPress={() => navigation.navigate('Auth')}
            />
          }
        />
      </RTLSafeAreaView>
    );
  }

  return (
    <RTLSafeAreaView style={styles.container}>
      {groupedOrders.length > 0 ? (
        <RTLSectionList
          sections={groupedOrders}
          renderItem={renderOrderItem}
          renderSectionHeader={renderSectionHeader}
          keyExtractor={(item) => item.id}
          ListHeaderComponent={renderHeader}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          stickySectionHeadersEnabled={false}
        />
      ) : (
        <RTLView style={styles.emptyContainer}>
          {renderHeader()}
          <EmptyState
            icon="receipt-outline"
            title={searchQuery || selectedFilter !== 'all' ? t('orders.noOrdersFound') : t('orders.noOrdersYet')}
            description={
              searchQuery || selectedFilter !== 'all'
                ? t('orders.adjustSearchFilter')
                : t('orders.startShopping')
            }
            actionButton={
              !searchQuery && selectedFilter === 'all' ? (
                <Button
                  title={t('common.startShopping')}
                  onPress={() => navigation.navigate('Home')}
                />
              ) : undefined
            }
          />
        </RTLView>
      )}
    </RTLSafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.lg,
    backgroundColor: colors.background,
  },
  searchInput: {
    marginBottom: SPACING.md,
  },
  filterContainer: {
    paddingBottom: SPACING.md,
    gap: SPACING.sm,
  },
  filterChip: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    marginRight: SPACING.sm,
  },
  filterChipActive: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  filterChipText: {
    fontSize: FONT_SIZES.sm,
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  filterChipTextActive: {
    color: '#FFFFFF',
  },
  resultsInfo: {
    paddingTop: SPACING.sm,
  },
  resultsText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  listContent: {
    paddingBottom: SPACING.xl,
  },
  emptyContainer: {
    flex: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    backgroundColor: colors.background,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  sectionCount: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  orderCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    padding: SPACING.lg,
  },
  orderContent: {
    gap: SPACING.md,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  orderDate: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  orderItems: {
    gap: SPACING.xs,
  },
  itemsText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  itemsPreview: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderTotal: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#4CAF50',
  },
  orderActions: {
    padding: SPACING.sm,
  },
});
